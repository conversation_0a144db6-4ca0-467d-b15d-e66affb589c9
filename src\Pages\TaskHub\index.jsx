import React, { useState } from "react";
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import TruncatedCell from "../../Components/Tooltip/TruncatedCell";
import FilterPanel from "../../Components/Observation/FilterPanel";
import { IoFilter } from "react-icons/io5";
import TruncatedRow from "../../Components/Tooltip/TrucantedRow";
import { HiOutlineHandThumbUp, HiOutlineHandThumbDown } from "react-icons/hi2";
import { initialTasks } from "../../api/static";
import ViewTasks from "./ViewTasks";

const TaskHub = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showView, setShowView] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [filter, setFilter] = useState("All");
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);

  const handleFilterOpen = () => setIsFilterPanelOpen(true);

  

  const [tasks, setTasks] = useState(initialTasks);

  // Update recommendation status using the unique id
  const updateRecStatus = (id, newStatus) => {
    setTasks(prevData =>
      prevData.map(task =>
        task.id === id ? { ...task, recommend: newStatus } : task
      )
    );
  };
  const handleView = (req) => {
    setSelectedRequest(req);
    setShowView(true);
  };

  // Filter tasks based solely on recommendation filter
  const filteredDatas = tasks.filter(row =>
    filter === "All" ? true : row.recommend.toLowerCase() === "approve"
  );

  const columns = [
    {
      name: <TruncatedCell text="Task Id" />,
      selector: row => <TruncatedRow text={row.taskId} />,
    //   cell: row => <TruncatedRow text={row.taskId} />,
      cell: (row) => (
        <span
          className="underline underline-offset-1 cursor-pointer"
          onClick={() => handleView(row)}
        >
          {row.taskId}
        </span>
      ),
    },
    {
      name: <TruncatedCell text="Type" />,
      selector: row => row.type,
      cell: row => <TruncatedRow text={row.type} />,
    },
    {
      name: <TruncatedCell text="Request For" />,
      selector: row => row.requestFor,
      cell: row => <TruncatedRow text={row.requestFor} />,
    },
    {
      name: <TruncatedCell text="Item(s)" />,
      selector: row => row.items,
      cell: row => <TruncatedRow text={row.items} />,
    },
    {
      name: <TruncatedCell text="Request Id" />,
      selector: row => row.requestId,
      cell: row => <TruncatedRow text={row.requestId} />,
    },
    {
      name: <TruncatedCell text="Requested By" />,
      selector: row => <TruncatedRow text={row.requestedBy} />,
      center: true,
    },
    {
      name: <TruncatedCell text="Created By" />,
      selector: row => row.createdBy,
      cell: row => <TruncatedRow text={row.createdBy} />,
      center: true,
    },
    {
      name: <TruncatedCell text="Assignee" />,
      selector: row => <TruncatedRow text={row.assignee} />,
      cell: row => <TruncatedRow text={row.assignee} />,
      center: true,
    },
    {
      name: <TruncatedCell text="Justification" />,
      selector: row => <TruncatedRow text={row.justification} />,
      cell: row => <TruncatedRow text={row.justification} />,
      center: true,
    },
    {
      name: <TruncatedCell text="Recommend" />,
      selector: row =>
        row.recommend === "Approve" ? (
          <HiOutlineHandThumbUp className="text-[#4F2683] text-xl" />
        ) : (
          <HiOutlineHandThumbDown className="text-[#8F8F8F] text-xl" />
        ),
    },
    {
      name: "Actions",
      cell: row => (
        <div className="flex gap-2">
          <button
            className="flex gap-1 p-1 whitespace-nowrap w-full border text-[#4F2683] rounded-full"
            onClick={() => updateRecStatus(row.id, "Approve")}
          >
            <HiOutlineHandThumbUp className="text-xl" />
            <p>Approve</p>
          </button>
          <button
            className="flex gap-1 p-1 whitespace-nowrap w-full border text-[#8F8F8F] rounded-full"
            onClick={() => updateRecStatus(row.id, "Deny")}
          >
            <HiOutlineHandThumbDown className="text-xl" />
            <p>Deny</p>
          </button>
        </div>
      ),
      center: true,
    },
  ];

  return (
    <div className="flex flex-col px-8 py-4 pl-20 pt-20">
      <div className="mb-6">
        <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">Task Hub</h2>
      </div>
      <div className="mb-4">
        <FilterButtons
          filter={filter}
          onFilterChange={setFilter}
          filterOptions={[
            { label: "All", value: "All" },
            { label: "Active", value: "Active" },
          ]}
        />
      </div>

      <div className="bg-white rounded-[10px]">
        <GenericTable
          fixedHeaderScrollHeight="420px"
          title="Tasks"
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          columns={columns}
          data={filteredDatas}
          showSearch={true}
          showAddButton={false}
          Checkboxes={true}
          highlightOnHover={false}
          passValueToSearch={true}
          extraControls={
                    <IoFilter
                      className="bg-white shadow-sm border items-center p-[5px] text-[#4F2683] h-[32px] w-8 rounded cursor-pointer"
                      onClick={handleFilterOpen}
                    />
                  }
        />
      </div>
      {showView && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white p-1 shadow-lg rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewTasks
                  taskData={selectedRequest}
                  onClose={() => setShowView(false)}
              />
            </div>
          </div>
        </div>
      )}
      <FilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
      />
    </div>
  );
};

export default TaskHub;
