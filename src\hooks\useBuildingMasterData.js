import { useState, useCallback, useEffect, useMemo } from "react";
import { getMasterData } from "../api/global";
import { toast } from "react-toastify";

// Module-level cache to persist master data across unmounts
let buildingMasterDataCache = null;

export const useBuildingMasterData = () => {
  const [masterData, setMasterData] = useState(
    buildingMasterDataCache || {
      building_status: [],
      building_type: [],
      building_occupancy_type: [],
    }
  );

  const fetchMasterData = useCallback(async () => {
    if (buildingMasterDataCache) return; // Data already cached
    try {
      const res = await getMasterData({
        groups: ["building_status", "building_type", "building_occupancy_type"],
      });
      buildingMasterDataCache = res.data;
      setMasterData(res.data);
    } catch (error) {
      toast.error("Error fetching building master data");
    }
  }, []);

  useEffect(() => {
    if (!buildingMasterDataCache) {
      fetchMasterData();
    }
  }, [fetchMasterData]);

  // Prepare dropdown options using memoization
  const statusOptions = useMemo(() => {
    return masterData.building_status.map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.building_status]);

  const typeOptions = useMemo(() => {
    return masterData.building_type.map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.building_type]);

  const occupancyOptions = useMemo(() => {
    return masterData.building_occupancy_type.map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.building_occupancy_type]);

  return { masterData, statusOptions, typeOptions, occupancyOptions };
};
