import React from "react";
import Button from "../Global/Button";
import Input from "../Global/Input";

const AddComments = ({ onSave, onClose, newEntry, setNewEntry }) => {
  return (
    <div className="fixed inset-0 w-full flex items-center justify-center bg-black z-50 bg-opacity-50">
      <div className="bg-white p-6 w-[70%] rounded shadow-lg">
        <div className="flex justify-between">
          <h3 className="text-[30px] font-normal text-[#4F2683] mb-2">Add Comment</h3>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        <hr className="mb-4" />
        <form className="bg-white p-2 rounded-lg">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">Details</h2>

          <div className="flex">
            <label
              htmlFor="comment"
              className="flex items-center h-11 w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Comment
            </label>
            <div className="w-3/4">
              <Input
                className="border p-2 w-full mb-4"
                placeholder="Enter comment"
                value={newEntry.comment}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, comment: e.target.value })
                }
              />
            </div>
          </div>

          <div className="flex">
            <label
              htmlFor="addedBy"
              className="flex items-center h-11 w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Added by
            </label>
            <div className="w-3/4">
              <Input
                className="border p-2 w-full mb-4"
                placeholder="Enter name"
                value={newEntry.addedBy}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, addedBy: e.target.value })
                }
              />
            </div>
          </div>

          <div className="flex">
            <label
              htmlFor="addedOn"
              className="flex items-center h-11 w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Added On
            </label>
            <div className="w-3/4">
              <Input
                type="date"
                className="border p-2 w-full mb-4"
                placeholder="Select date"
                value={newEntry.addedOn}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, addedOn: e.target.value })
                }
              />
            </div>
          </div>

          <div className="flex justify-center gap-4">
            <Button type="cancel" onClick={onClose} label="Cancel" />
            <Button type="primary" onClick={onSave} label="Save" />
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddComments;
