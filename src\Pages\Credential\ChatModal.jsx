// ChatModal.js
import React, { useState } from "react";
import { useTranslation } from 'react-i18next';
import Button from "../../Components/Global/Button";

const ChatModal = ({ onClose, credential }) => {
  const { t } = useTranslation();
  const [message, setMessage] = useState("");

  const handleSend = () => {
    // Example: You could make an API call or handle chat logic here
    console.log(`Sending message to EID: ${credential.EID}`, message);
    setMessage("");
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 py-6">
      <div className="bg-white w-full h-full max-w-md p-4 rounded-lg shadow-lg overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h2 className="text-[30px] text-[#4F2683] font-normal">{t('chat_modal.title')}</h2>
          <button
            onClick={onClose}
            className="flex items-center justify-center bg-[#4F2683] text-white text-2xl rounded-full h-8 w-8 hover:bg-[#6A3BAA]"
          >
            &times;
          </button>
        </div>
        <hr className="mb-4 mt-2" />

        {/* Body */}
        <div className="flex flex-col space-y-4">
          <div className="flex flex-col">
            <label className="font-medium">{t('chat_modal.eid')}</label>
            <span>{credential?.EID}</span>
          </div>
          <div className="flex flex-col">
            <label className="font-medium">{t('chat_modal.name')}</label>
            <span>{credential?.name}</span>
          </div>

          <div className="flex flex-col mt-4">
            <label className="font-medium">{t('chat_modal.enter_message')}</label>
            <textarea
              className="border border-gray-300 rounded p-2 w-full"
              rows={5}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
            />
          </div>

          <div className="flex justify-end gap-4">
            <Button type="cancel" label={t('chat_modal.cancel')} onClick={onClose} />
            <Button
              type="primary"
              label={t('chat_modal.send')}
              onClick={handleSend}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatModal;
