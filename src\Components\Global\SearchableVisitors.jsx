import React, { useState, useEffect, useRef } from "react";
import { searchGuests } from "../../api/guest"; // Adjust path as needed

const SearchableVisitor = ({ value = [], onSelect }) => {
  const [selectedIdentities, setSelectedIdentities] = useState(value);
  const [userInput, setUserInput] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const containerRef = useRef(null);
  const debounceTimeout = useRef(null);

  // Sync with parent value changes.
  useEffect(() => {
    setSelectedIdentities(value);
  }, [value]);

  // Fetch guests from API as user types (debounced)
  useEffect(() => {
    if (!userInput) {
      setSearchResults([]);
      setLoading(false);
      setError(null);
      return;
    }
    setLoading(true);
    setError(null);

    if (debounceTimeout.current) clearTimeout(debounceTimeout.current);

    debounceTimeout.current = setTimeout(async () => {
      try {
        // Try searching by email first, then by phone if input is numeric
        let params = {};
        if (/^\d+$/.test(userInput)) {
          params.phone = userInput;
        } else {
          params.email = userInput;
        }
       const response = await searchGuests(params);
console.log("Fetched guests:", response);

if (Array.isArray(response.data)) {
  setSearchResults(response.data);
} else {
  setSearchResults([]);
}
        // console.log("Fetched guests:", data); // <-- Added console log
        // setSearchResults(Array.isArray(data) ? data : []);
        setLoading(false);
      } catch (err) {
        setError("Failed to fetch guests");
        setSearchResults([]);
        setLoading(false);
      }
    }, 400);

    return () => {
      if (debounceTimeout.current) clearTimeout(debounceTimeout.current);
    };
  }, [userInput]);

  // Toggle selection when a checkbox is clicked.
  const handleCheckboxChange = (identity) => {
    setSelectedIdentities((prev) => {
      const isAlreadySelected = prev.some((item) => item.id === identity.id);
      let updated;
      if (isAlreadySelected) {
        updated = prev.filter((item) => item.id !== identity.id);
      } else {
        updated = [...prev, identity];
      }
      if (onSelect) {
        onSelect(updated);
      }
      // Clear the search input after selecting/deselecting.
      setUserInput("");
      return updated;
    });
  };

  // Remove a chip.
  const handleRemoveChip = (id) => {
    setSelectedIdentities((prev) => {
      const updated = prev.filter((item) => item.id !== id);
      if (onSelect) {
        onSelect(updated);
      }
      return updated;
    });
  };

  // Close the dropdown if clicking outside of the container.
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Handle input change
  const handleInputChange = (e) => {
    setUserInput(e.target.value);
    setIsDropdownOpen(true);
  };

  return (
    <div className="relative w-full" ref={containerRef}>
      {/* Input container with chips and search field */}
      <div className="flex flex-wrap items-center gap-2 border rounded-md p-1">
        {selectedIdentities.map((identity) => (
          <div
            key={identity.guest_id}
            className="flex items-center bg-[#E4D7F3] px-2 py-1 rounded-full"
          >
            <img
              src={identity.image}
              alt={identity.first_name}
              className="w-5 h-5 rounded-full mr-1"
            />
            <span className="text-sm">{identity.firstName}</span>
            <button
              type="button"
              className="ml-1 text-white bg-[#4F2386] rounded-full w-4 h-4 flex items-center justify-center text-xs"
              onClick={() => handleRemoveChip(identity.guest_id)}
            >
              &times;
            </button>
          </div>
        ))}
        <input
          type="text"
          placeholder={selectedIdentities.length === 0 ? "Search Identity" : ""}
          className="flex-grow p-1 focus:outline-none"
          value={userInput}
          onChange={handleInputChange}
          onFocus={() => setIsDropdownOpen(true)}
        />
      </div>

      {/* Dropdown for filtered visitors */}
      {isDropdownOpen && userInput && (
        <div className="absolute w-full mt-1 bg-white border rounded-md shadow-lg max-h-40 overflow-auto z-10">
          {loading ? (
            <p className="p-2 text-gray-500">Loading...</p>
          ) : error ? (
            <p className="p-2 text-red-500">{error}</p>
          ) : searchResults.length > 0 ? (
            searchResults.map((identity) => {
              const isChecked = selectedIdentities.some(
                (item) => item.id === identity.id
              );
              return (
                <label
                  key={identity.guest_id}
                  className="p-2 hover:bg-gray-100 flex items-center justify-between cursor-pointer"
                >
                  <div className="flex items-center gap-2">
                    <img
                      src={identity.image}
                      alt={identity.first_name}
                      className="w-10 h-10 rounded-md"
                    />
                    <div>
                      <h3 className="text-[16px] font-medium">
                        {identity.firstName} {identity.lastName}
                      </h3>
                      <p className="text-[14px] text-gray-600">
                        {identity.email}
                      </p>
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    className="cursor-pointer"
                    checked={isChecked}
                    onChange={() => handleCheckboxChange(identity)}
                  />
                </label>
              );
            })
          ) : (
            <p className="p-2 text-gray-500">No results found</p>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchableVisitor;
