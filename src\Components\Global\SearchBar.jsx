import React from "react";
import iconsSrc from "../../Images/SearchIcon.svg";

const SearchBar = ({
  placeholder,
  onSearch,
  iconSrc,
  wrapperClassName = "",
  inputClassName = "",
  buttonClassName = "",
  borderColor,
  onInputChange, 
  onClick, 
  value, 
}) => {
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    if (onInputChange) {
      onInputChange(newValue); 
    }
  };

  const handleSearch = (e) => {
    e.stopPropagation(); // Prevents triggering the parent's onClick
    if (onSearch) {
      onSearch(value); 
    }
  };

  return (
    <div
      className={`sm:w-[340px] h-[32px] rounded-[60px] shadow-[0px_3.941415548324585px_7.88283109664917px_4.926765441894531px_rgba(79,38,131,0.06)] border border-[#4f2683] flex items-center px-4 pl-3 md:pl-4 mx-auto ${wrapperClassName}`}
      style={{ borderColor }}
      onClick={onClick} 
    >
      <input
        type="text" 
        placeholder={placeholder}
        className={`flex-grow text-[#4F2683] text-sm font-normal font-['Poppins'] outline-none placeholder-[#4F2683] ${inputClassName}`}
        onChange={handleInputChange}
        value={value} 
      />
      <button
        type="button"
        className={`w-[18px] h-[18px] relative overflow-hidden ${buttonClassName}`}
        onClick={handleSearch}
      >
        <img src={iconSrc || iconsSrc} alt="Search icon" className="w-5 h-5" />
      </button>
    </div>
  );
};

export default SearchBar;
