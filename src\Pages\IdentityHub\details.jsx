import React, { useCallback, useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { IoIosArrowBack } from "react-icons/io";

import Demographic from "../../Components/IdentityHub/Identity";
import Organization from "../../Components/IdentityHub/Organization/index";
import AccessAreas from "../../Components/IdentityHub/AccessArea/AccessAreas";
import DetailsCard from "../../Components/Global/DetailsCard";
import EditPhotoModal from "../../Components/Global/ImageAndCamera/EditPhotoModal";
import userImg from "../../Images/Building.svg";
import HistoryTable from "../../Components/Observation/HistoryTable";
import Training from "../../Components/IdentityHub/Training/Training";
import Delegate from "../../Components/IdentityHub/Delegates/Delegate";
import Vehicle from "../../Components/IdentityHub/Vehicles/Vehicle";
import Tasks from "../../Components/IdentityHub/Tasks/tasks";
import InterfaceStatus from "../../Components/IdentityHub/InterfaceStatus/InterfaceStatus";
import Requests from "../../Components/IdentityHub/Requests/Requests";
import Card from "../../Components/IdentityHub/Card/Card";
import Documents from "../../Components/IdentityHub/Documents/Document";

import { getIdentityDetails } from "../../api/identity";

const IdentityDetails = () => {
  const [searchParams] = useSearchParams();
  const identityId = searchParams.get("identity_id");
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [identityDetails, setIdentityDetails] = useState(null);
  const [selectedTab, setSelectedTab] = useState("Demographic Information");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);

  const fetchBasicIdentity = useCallback(async (id) => {
    setLoading(true);
    try {
      const res = await getIdentityDetails(id);
      setIdentityDetails(res.data);
    } catch (error) {
      toast.error("Error fetching identity details");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (identityId) {
      fetchBasicIdentity(identityId);
    } else {
      toast.error("Missing identity ID in URL");
    }
  }, [identityId, fetchBasicIdentity]);

  const handleImageCaptured = (imageSrc) => {
    setProfileImage(imageSrc);
    setIsModalOpen(false);
  };

  const handleHistoryOpen = () => setIsHistoryPanelOpen(true);

  const Options = [
    "Demographic Information",
    "Organization",
    "Cards",
    "Access Areas",
    "Documents",
    "Training",
    "Delegates",
    "Vehicles",
    "Requests",
    "Tasks",
    "Interface Status",
  ];

  if (loading) return <div>Loading identity details...</div>;
  if (!identityDetails)
    return <div>No identity found or an error occurred.</div>;

  const { first_name, last_name, start_date, end_date, status, job_title } =
    identityDetails;
  const name = `${first_name} ${last_name}`;
  const jobTitle = `${job_title}`;
  const statusLabel =
    status === 0
      ? "Active"
      : status === 1
      ? "Terminated"
      : status === 2
      ? "Suspended"
      : "Unknown";

  const options = { year: "numeric", month: "long", day: "numeric" };
  const startDateFormatted = new Date(start_date).toLocaleDateString(
    undefined,
    options
  );
  const endDateFormatted = new Date(end_date).toLocaleDateString(
    undefined,
    options
  );

  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-20 pt-20">
      {/* Header Section */}
      <div className="flex items-center text-[#4F2683]">
        <div
          className="flex items-center gap-1 cursor-pointer"
          onClick={() => navigate("/identity-hub")}
        >
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">Identity-Hub</h2>
        </div>
      </div>

      {/* Details Card */}
      <DetailsCard
        OpenPhotoModal={() => setIsModalOpen(true)}
        handleHistoryOpen={handleHistoryOpen}
        profileImage={profileImage}
        defaultImage={userImg}
        name={name}
        showHistoryButton={true}
        additionalFields={[
          { label: "Job Title", value: jobTitle },
          { label: "Status", value: statusLabel },
          {
            label: "Start Date",
            value: new Date(start_date).toLocaleDateString(undefined, options),
          },
          {
            label: "End Date",
            value: new Date(end_date).toLocaleDateString(undefined, options),
          },
        ]}
      />

      {/* Tab Navigation */}
      <div className="flex">
        <div className="w-[12%] mt-6">
          {Options.map((tab) => (
            <button
              key={tab}
              className={`block w-full text-left p-2 mb-2 ${
                selectedTab === tab
                  ? "text-[#4F2683] border-l-2 border-[#4F2683]"
                  : "text-gray-700"
              }`}
              onClick={() => setSelectedTab(tab)}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="w-[88%] pt-5">
          {selectedTab === "Demographic Information" && <Demographic />}
          {selectedTab === "Organization" && <Organization />}
          {selectedTab === "Cards" && <Card />}
          {selectedTab === "Access Areas" && <AccessAreas />}
          {selectedTab === "Documents" && <Documents />}
          {selectedTab === "Training" && <Training />}
          {selectedTab === "Delegates" && <Delegate />}
          {selectedTab === "Vehicles" && <Vehicle />}
          {selectedTab === "Requests" && <Requests />}
          {selectedTab === "Tasks" && <Tasks />}
          {selectedTab === "Interface Status" && <InterfaceStatus />}
        </div>
      </div>

      {/* Modals and History */}
      <HistoryTable
        isOpen={isHistoryPanelOpen}
        onClose={() => setIsHistoryPanelOpen(false)}
      />
      {isModalOpen && (
        <EditPhotoModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleImageCaptured}
        />
      )}
    </div>
  );
};

export default IdentityDetails;
