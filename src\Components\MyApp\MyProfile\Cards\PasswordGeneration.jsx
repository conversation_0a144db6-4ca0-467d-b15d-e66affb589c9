import React, { useState } from "react";
import Button from "../../../Global/Button";
import Input from "../../../Global/Input/Input";

const PasswordGeneration = ({ onClose }) => {
  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (formData.newPassword === formData.confirmPassword) {
      console.log("Password successfully created:", formData.newPassword);
      onClose();
    } else {
      alert("Passwords do not match!");
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-2xl bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Add / Update Pin
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        <form onSubmit={handleSubmit}>

          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              New Pin *
            </label>
            <div className="w-3/4">
              <Input
                type="password"
                name="newPassword"
                placeholder="Enter New Pin"
                value={formData.newPassword}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Re-Enter Pin *
            </label>
            <div className="w-3/4">
              <Input
                type="password"
                name="confirmPassword"
                placeholder="Re-Enter Pin"
                value={formData.confirmPassword}
                onChange={handleChange}
              />
            </div>
          </div>
          <div className="flex justify-center gap-4 mt-6">
            <Button type="cancel" label="Cancel" onClick={onClose} />
            <Button type="primary" label="Save" onClick={handleSubmit} />
          </div>
        </form>
      </div>
    </div>
  );
};

export default PasswordGeneration;
