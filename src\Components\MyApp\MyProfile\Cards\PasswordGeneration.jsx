import React, { useState } from "react";
import Button from "../../../Global/Button";
import Input from "../../../Global/Input/Input";

const PasswordGeneration = ({ onClose }) => {
  const [show, setShow] = useState(false);
  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: "",
  });

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (formData.newPassword === formData.confirmPassword) {
      console.log("Password successfully created:", formData.newPassword);
      onClose();
    } else {
      alert("Passwords do not match!");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add / Update Pin</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <div className="p-6">
        <form onSubmit={handleSubmit}>

          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              New Pin *
            </label>
            <div className="w-3/4">
              <Input
                type="password"
                name="newPassword"
                placeholder="Enter New Pin"
                value={formData.newPassword}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Re-Enter Pin *
            </label>
            <div className="w-3/4">
              <Input
                type="password"
                name="confirmPassword"
                placeholder="Re-Enter Pin"
                value={formData.confirmPassword}
                onChange={handleChange}
              />
            </div>
          </div>
          <div className="flex justify-center gap-4 mt-6">
            <Button type="cancel" label="Cancel" onClick={onClose} />
            <Button type="primary" label="Save" onClick={handleSubmit} />
          </div>
        </form>
        </div>
      </div>
    </div>
  );
};

export default PasswordGeneration;
