import React, { useState, useEffect, useCallback } from "react";
import GenericTable from "../../GenericTable";
import AddTrainingForm from "./AddTrainingForm";
import ViewEditTrainingForm from "./ViewEditTrainingForm";
import Loader from "../../Loader.jsx";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import TruncatedCell from "../../Tooltip/TruncatedCell.jsx";
import TruncatedRow from "../../Tooltip/TrucantedRow.jsx";
import { FaEdit } from "react-icons/fa";
import Delete from "../../../Images/Delete.svg";
import { getTraining, deleteTraining } from "../../../api/identity";
import { useLocation } from "react-router-dom";

const Training = () => {
  const [data, setData] = useState([]);
  const [originalData, setOriginalData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const [showAddTrainingForm, setShowAddTrainingForm] = useState(false);
  const [showViewTrainingForm, setShowViewTrainingForm] = useState(false);
  const [selectedTraining, setSelectedTraining] = useState(null);


  // Get identity_id from URL parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id");

  // Fetch training data from API with debouncing
  const fetchTrainingData = useCallback(async () => {
    if (!identityId) return;

    setLoading(true);
    try {
      const params = {
        search: searchTerm || undefined,
      };
      const res = await getTraining(identityId, params);
      // Ensure we always set an array
      const dataArray = Array.isArray(res) ? res : [];
      setOriginalData(dataArray);
      setData(dataArray);
    } catch (error) {
      toast.error("Error fetching training data.");
      console.error("Error fetching training data:", error);
      setOriginalData([]);
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [identityId, searchTerm]);



  useEffect(() => {
    fetchTrainingData();
  }, [fetchTrainingData]);

  const handleAdd = () => {
    setShowAddTrainingForm(true);
  };

  const handleCloseAddModal = () => {
    setShowAddTrainingForm(false);
  };

  const handleView = (row) => {
    setSelectedTraining(row);
    setShowViewTrainingForm(true);
  };

  const handleCloseViewModal = () => {
    setShowViewTrainingForm(false);
  };

  const handleDelete = async (training) => {
    try {
      await deleteTraining(training.training_id || training.id);
      setData((prevData) =>
        prevData.filter((item) => (item.training_id || item.id) !== (training.training_id || training.id))
      );
      fetchTrainingData(); // Refresh data from server
      toast.success("Training deleted successfully!");
    } catch (error) {
      toast.error("Failed to delete training. Please try again.");
      console.error("Error deleting training:", error);
    }
  };

  const handleUpdate = (updatedTraining) => {
    setData((prevData) =>
      prevData.map((item) =>
        (item.training_id || item.id) === (updatedTraining.training_id || updatedTraining.id) ? updatedTraining : item
      )
    );
    fetchTrainingData(); // Refresh data from server
    toast.success("Training updated successfully!");
    setShowViewTrainingForm(false);
  };

  const addTraining = (newTraining) => {
    setData((prev) => [
      { ...newTraining, training_id: Date.now() },
      ...prev,
    ]);
    fetchTrainingData(); // Refresh data from server
    toast.success("Training added successfully!");
  };

  // Handle sorting client-side (no API call)
  const handleSort = (column, sortDirection) => {
    console.log('Client-side sorting:', column.id || column.selector, sortDirection);

    const sortKey = column.id || 'name';
    const sortedData = [...originalData].sort((a, b) => {
      let aValue = a[sortKey];
      let bValue = b[sortKey];

      // Handle different data types
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
      }
      if (typeof bValue === 'string') {
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });

    setData(sortedData);
  };

  const columns = [
    {
      id: 'name',
      name: "Name",
      selector: (row) => row.name,
      cell: (row) => (
        <span
          style={{ textDecoration: "underline", cursor: "pointer" }}
          onClick={() => handleView(row)}
        >
         <TruncatedRow  text={row.name}/>
        </span>
      ),
      sortable: true,
    },
    {
      id: 'course_number',
      name: <TruncatedCell text="Course Number" />,
      selector: (row) => row.course_number,
      cell: (row) => <TruncatedRow text={row.course_number} />,
      sortable: true,
    },
    {
      id: 'category',
      name: <TruncatedCell text="Category"/>,
      selector: (row) => row.category,
      cell: (row) => <TruncatedRow text={row.category} />,
      sortable: true,
    },
    {
      id: 'course_type',
      name: <TruncatedCell text="Course Type" />,
      selector: (row) => row.course_type,
      cell: (row) => {
        const courseTypeMap = { 1: "Online", 2: "Offline", 3: "Hybrid" };
        return <TruncatedRow text={courseTypeMap[row.course_type] || row.course_type} />;
      },
      sortable: true,
    },
    {
      id: 'recurrence',
      name: <TruncatedCell text="Recurrence" />,
      selector: (row) => row.recurrence || row.recurrance,
      cell: (row) => {
        const recurrenceMap = { 1: "One Time", 2: "Monthly", 3: "Quarterly", 4: "Yearly" };
        const value = row.recurrence || row.recurrance;
        return <TruncatedRow text={recurrenceMap[value] || value} />;
      },
      sortable: true,
    },
    {
      id: 'due_date',
      name:<TruncatedCell text="Due Date"/>,
      selector: (row) => row.due_date,
      cell: (row) => <TruncatedRow text={row.due_date} />,
      sortable: true,
    },
    {
      id: 'date_completed',
      name: <TruncatedCell text="Date Completed" />,
      selector: (row) => row.date_completed,
      cell: (row) => <TruncatedRow text={row.date_completed} />,
      sortable: true,
    },
    {
      id: 'score',
      name: <TruncatedCell text="Score"/>,
      selector: (row) => row.score,
      cell: (row) => <TruncatedRow text={row.score} />,
      sortable: true,
    },
    {
      id: 'status',
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => {
        const statusMap = { 1: "Pass", 2: "Fail", 3: "Pending" };
        const statusText = statusMap[row.status] || row.status;
        return (
          <span
            className={`w-20 py-1 flex justify-center items-center rounded-full ${
              row.status === 1 || row.status === "Pass" || row.status === "pass"
                ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
                : row.status === 2 || row.status === "Fail" || row.status === "fail"
                ? "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
                : "bg-[#FFA50014] bg-opacity-8 text-[#4F2683]"
            }`}
          >
            {statusText}
          </span>
        );
      },
      sortable: true,
    },
    {
      name: "Action",
      cell: (row) => (
        <>
        <div className="flex space-x-2 items-center justify-center">
          <FaEdit
            size={32}
            className="p-1.5 bg-[#F0EDF5] flex justify-center rounded-lg cursor-pointer text-green-500 items-center"
            onClick={() => handleView(row)}
            />
          <img src={Delete} alt="Delete" className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer" onClick={() => handleDelete(row)} />
            </div>
        </>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ];

  return (
    <div className="bg-white rounded-[10px]">
      {loading ? (
        <Loader />
      ) : (
        <GenericTable
          title="Trainings"
          searchTerm={searchTerm}
          showSearch={true}
          onSearchChange={(e) => setSearchTerm(e.target.value)}
          onAdd={handleAdd}
          columns={columns}
          data={data}
          showAddButton={true}
          onSort={handleSort}
        />
      )}

      {showAddTrainingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <AddTrainingForm onClose={handleCloseAddModal} addTraining={addTraining} />
            </div>
          </div>
        </div>
      )}

      {showViewTrainingForm && selectedTraining && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditTrainingForm
                onClose={handleCloseViewModal}
                trainingData={selectedTraining}
                onUpdate={handleUpdate}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Training;
