import React from 'react'

function useGroup() {
  return (
    <div className="flex justify-center items-center h-screen bg-gray-100">
      <div className="text-center p-6 bg-white rounded-lg shadow-lg">
        <h1 className="text-3xl font-semibold text-blue-600">Welcome to the Group Page</h1>
        <p className="mt-4 text-gray-700">
          Here, you can manage and interact with your group members in a seamless way.
        </p>
        <button className="mt-6 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
          Start Group Activity
        </button>
      </div>
    </div>
  )
}

export default useGroup
