import React, { useRef } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import Button from "../Global/Button";
import ImageCapture from "../Global/ImageAndCamera/ImageCaptureForForm";
import Input from "../Global/Input/Input";
import DateInput from "../Global/Input/DateInput";
import CustomDropdown from "../Global/CustomDropdown";
import { createGuest } from "../../api/Appointments";
import { usePatientGuestTypeMasterData } from "../../hooks/usePatientGuestTypeMasterData";
import { useSelector } from "react-redux"; // <-- Add this line

const WalkInVisitForm = ({ fieldsToRender, onAddGuest, onClose, appointmentId, ...props }) => {
  const selectedFacilityName = useSelector((state) => state.facility.selectedFacilityName);
  const { relationshipTypes } = usePatientGuestTypeMasterData();

  const submitActionRef = useRef("");
  const isSubmittingRef = useRef(false);

  // Relationship types ko numeric value dene ke liye mapping
  const relationshipTypeMapping = relationshipTypes.reduce((acc, type, index) => {
    acc[type] = index + 1;
    return acc;
  }, {});

   const relationshipOptions = relationshipTypes.map(t => ({
    label: t.value,
    value: t.key,
  }));
  // const facilityOptions = ["xray-room", "operation-theater"];
  const durationOptions = ["2:00 Hour", "4:00 Hour", "8:00 Hour", "12:00 Hour"];

  const currentTime = new Date().toLocaleTimeString([], {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
  });

  const initialValues = {
    firstName: "",
    lastName: "",
    dob: "",
    guestMail: "",
    phoneNumber: "",
    relationship: relationshipTypes[0] || "Other",
    image: "",
    screening: true,
  };

  const validationSchema = Yup.object({
    firstName: Yup.string().required("First Name is required"),
    lastName: Yup.string().required("Last Name is required"),
  });

  // const facilityMapping = {
  //   "xray-room": 1,
  //   "operation-theater": 2,
  // };

  const durationMapping = {
    "2:00 Hour": 2,
    "4:00 Hour": 4,
    "8:00 Hour": 8,
    "12:00 Hour": 12,
  };

  const handleSubmit = async (values, { resetForm }) => {
    if (isSubmittingRef.current) return; // Duplicate submissions rokne ke liye
    isSubmittingRef.current = true;

    const guestPayload = {
      first_name: values.firstName,
      last_name: values.lastName,
      email: values.guestMail,
      phone: values.phoneNumber,
      organization: "Health Corp",
      guest_type: 0,
      relationship_type: relationshipTypeMapping[values.relationship] || 0,
      relationship_status: 0,
      is_emergency_contact: true,
      emergency_contact_priority: 3,
      appointment_id: appointmentId,
      // start_date: values.startDate.toISOString().split("T")[0],
      // start_time: values.startTime,
      // duration: durationMapping[values.duration],
      // facility: facilityMapping[values.facility],
      screening: values.screening,
    };

    try {
      const response = await createGuest(guestPayload);
      console.log("API Response:", response);

      if (submitActionRef.current === "addMore") {
        toast.success("Guest added successfully!", {
          autoClose: 2000,
          pauseOnHover: false,
        });
        resetForm({
          values: {
            ...values,
            firstName: "",
            lastName: "",
            guestMail: "",
            phoneNumber: "",
            image: "",
            // Retain dob and relationship values
            dob: values.dob,
            relationship: values.relationship,
          },
          errors: {},
          touched: {},
        });
      } else if (submitActionRef.current === "save") {
        toast.success("Guest saved successfully!", {
          autoClose: 2000,
          pauseOnHover: false,
        });
        onClose();
      }

      // Parent component ko updated guest list fetch karne ke liye trigger karo
      onAddGuest();
    } catch (error) {
      console.error("API Error:", error.response?.data || error.message);
      toast.error(
        error.response?.data?.message || "Failed to create guest. Please try again."
      );
    } finally {
      isSubmittingRef.current = false;
    }
  };

  const patientFields = ["patientName", "facility", "escortName", "startDate", "startTime", "endTime"];
  const hasPatientFields = fieldsToRender.some((field) => patientFields.includes(field));

  const selectedFacility = localStorage.getItem("selectedFacility") || "Select Facility";;

  return (
    <div className="px-12">
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        validateOnMount={false}
      >
        {({ setFieldValue, values, submitForm, touched, errors, handleBlur }) => (
          <Form className="space-y-4">
            {hasPatientFields && (
              <>
                <div className="flex justify-between">
                  <h1 className="text-xl md:text-lg font-bold text-[#4F2683]">Create Walk In Visit</h1>
                  <button
                    type="button"
                    className="text-xl text-white rounded-full h-8 w-8 bg-[#4F2683]"
                    onClick={onClose}
                  >
                    &times;
                  </button>
                </div>
                
              </>
            )}

            {/* Guest Information */}
            <div className="rounded-lg border p-4">
              <div className="flex justify-between mb-4">
                <h2 className="font-normal text-base md:text-lg text-[#4F2683]">Guest Information</h2>
                {!hasPatientFields && (
                  <button
                    type="button"
                    className="text-xl text-white rounded-full h-8 w-8 bg-[#4F2683]"
                    onClick={onClose}
                  >
                    &times;
                  </button>
                )}
              </div>
              <div>
                
                <ImageCapture
                  onImageCaptured={(img) => setFieldValue("image", img)}
                  onImageUploaded={(img) => setFieldValue("image", img)}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-16">
                {fieldsToRender.includes("firstName") && (
                  <div className="w-full">
                    <Input
                      type="text"
                      label="First Name*"
                      name="firstName"
                      value={values.firstName || ""}
                      placeholder="Enter First Name"
                      onChange={(e) => setFieldValue("firstName", e.target.value)}
                      onBlur={handleBlur}
                    />
                    {touched.firstName && errors.firstName && (
                      <div className="text-red-500 text-sm">{errors.firstName}</div>
                    )}
                  </div>
                )}
                {fieldsToRender.includes("lastName") && (
                  <div className="w-full">
                    <Input
                      type="text"
                      label="Last Name*"
                      name="lastName"
                      value={values.lastName || ""}
                      placeholder="Enter Last Name"
                      onChange={(e) => setFieldValue("lastName", e.target.value)}
                      onBlur={handleBlur}
                    />
                    {touched.lastName && errors.lastName && (
                      <div className="text-red-500 text-sm">{errors.lastName}</div>
                    )}
                  </div>
                )}
                {fieldsToRender.includes("dob") && (
                  <div className="w-full">
                    <DateInput
                      label="Date of Birth"
                      name="dob"
                      value={values.dob}
                      className="h-11"
                      onChange={(date) => setFieldValue("dob", date)}
                      placeholder="Select a date"
                      onBlur={handleBlur}
                    />
                  </div>
                )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                {fieldsToRender.includes("guestMail") && (
                  <div className="w-full">
                    <Input
                      type="email"
                      label="Email"
                      name="guestMail"
                      value={values.guestMail}
                      placeholder="Enter Email"
                      onChange={(e) => setFieldValue("guestMail", e.target.value)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                {fieldsToRender.includes("phoneNumber") && (
                  <div className="w-full">
                    <Input
                      type="tel"
                      label="Phone Number"
                      name="phoneNumber"
                      value={values.phoneNumber}
                      placeholder="Enter Phone Number"
                      onChange={(e) => setFieldValue("phoneNumber", e.target.value)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                {fieldsToRender.includes("relationship") && (
                  <div className="w-full">
                    <h2>Relationship</h2>
                    <CustomDropdown
                        options={relationshipOptions}
                      value={relationshipOptions.find(opt => opt.value === values.relationship)}
                      onSelect={opt => {setFieldValue("relationship", opt)}}
                      bgColor="bg-[white] text-black"
                      textColor="text-black"
                      hoverBgColor="hover:bg-[#4F2683]"
                      borderColor="border-gray-300"
                      rounded="rounded-[10px]"
                      className="p-2 border h-11 rounded-[10px] focus:outline-none focus:ring-1"
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="flex gap-4 pl-4 justify-center pb-4">
              <Button
                buttonType="button"
                type="primary"
                onClick={() => {
                  submitActionRef.current = "addMore";
                  submitForm();
                }}
                label="Add More"
              />
              <Button
                type="primary"
                onClick={() => {
                  submitActionRef.current = "save";
                  submitForm();
                }}
                label="Save"
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default WalkInVisitForm;
