// GuestListTable.jsx
import { FaWalking } from "react-icons/fa";
import { IoIosCamera, IoIosPrint } from "react-icons/io";
import { TbMessageQuestion } from "react-icons/tb";

const GuestListTable = ({ guestList, onPrint }) => (
  <div className="overflow-x-auto">
    <table className="table-auto w-full border-collapse border border-gray-300">
      <thead>
        <tr>
          <th className="border border-gray-300 p-2">Guest Name</th>
          <th className="border border-gray-300 p-2">Screening</th>
          <th className="border border-gray-300 p-2">Arrival Time</th>
          <th className="border border-gray-300 p-2">Action</th>
        </tr>
      </thead>
      <tbody className="overflow-x-auto">
        {guestList.length > 0 ? (
          guestList.map((guest, index) => (
            <tr key={index}>
              <td className=" p-2 flex items-center">
                {guest.image ? (
                  <img src={guest.image} alt={guest.guestName} className="w-10 h-10 rounded-full mr-2" />
                ) : (
                  <div className="w-10 h-10 bg-gray-300 rounded-full mr-2 flex items-center justify-center">
                    <span className="text-white text-xs">{guest.guestName.charAt(0)}</span>
                  </div>
                )}
                {guest.guestName}
              </td>
              <td className="border border-gray-300 p-2 justify-center items-center">
                {guest.screening ? '✔️' : '❌'}
              </td>
              <td className="border border-gray-300 p-2 justify-center items-center">{guest.arrivalTime}</td>
              <td className="flex flex-wrap gap-2 justify-center">
                <button className="bg-[#F1EEF5] p-3 rounded-lg">
                  <FaWalking className="text-2xl text-[#4F2683]" />
                </button>
                <button className="bg-[#F1EEF5] p-3 rounded-lg">
                  <IoIosCamera className="text-2xl text-[#4F2683]" />
                </button>
                <button className="bg-[#F1EEF5] p-3 rounded-lg">
                  <TbMessageQuestion className="text-2xl text-[#4F2683]" />
                </button>
                <button
                  onClick={() => onPrint(guest)}
                  className="bg-[#F1EEF5] p-3 rounded-lg"
                >
                  <IoIosPrint className="text-2xl text-[#4F2683]" />
                </button>
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan="4" className="text-center p-4 text-gray-500">
              No records found
            </td>
          </tr>
        )}
      </tbody>
    </table>
  </div>
);

export default GuestListTable;
