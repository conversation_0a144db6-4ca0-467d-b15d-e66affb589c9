import React, { useRef } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./DatePickerStyles.css";
import { FaRegCalendarAlt } from "react-icons/fa";
import CustomDropdown from "../CustomDropdown"; // make sure path is correct
const DateInput = ({ label, value, onChange, placeholder, error, className }) => {
  const datePickerRef = useRef(null);

  const handleClose = () => {
    if (datePickerRef.current) {
      datePickerRef.current.setOpen(false);
    }
  };

  return (
    <div className={className}>
      {label && <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>}
      <div className="relative">
        <DatePicker
          ref={datePickerRef}
          selected={value ? new Date(value) : null}
          onChange={(date) => onChange(date)}
          dateFormat="MMM-dd-yyyy"
          placeholderText={placeholder || "Select a date"}
          className="border h-10 p-2 focus:outline-none rounded-[6px] focus:ring-1 w-full focus:ring-[#4F2683]"
          shouldCloseOnSelect={true}
          onClickOutside={handleClose}
          renderCustomHeader={({ date, changeYear, changeMonth }) => {
            const years = Array.from({ length: 100 }, (_, i) => {
              const year = new Date().getFullYear() - i;
              return { label: year.toString(), value: year };
            });
        
            const months = [
              "January",
              "February",
              "March",
              "April",
              "May",
              "June",
              "July",
              "August",
              "September",
              "October",
              "November",
              "December",
            ].map((label, index) => ({ label, value: index }));
        
            return (
              <div className="flex justify-between items-center gap-2 px-3 py-2 bg-white rounded-t-md border-b">
                <div className="w-1/2">
                  <CustomDropdown
                    options={years}
                    value={date.getFullYear()}
                    onSelect={(val) => changeYear(val)}
                    placeholder="Select Year"
                    borderColor="border-gray-300"
                    rounded="rounded-md"
                  />
                </div>
                <div className="w-1/2">
                  <CustomDropdown
                    options={months}
                    value={date.getMonth()}
                    onSelect={(val) => changeMonth(val)}
                    placeholder="Select Month"
                    borderColor="border-gray-300"
                    rounded="rounded-md"
                  />
                </div>
              </div>
            );
          }}
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <FaRegCalendarAlt className="h-5 w-5 text-gray-400" />
        </div>
      </div>
      {error && <div className="text-red-500 text-sm">{error}</div>}
    </div>
  );
};

export default DateInput;
