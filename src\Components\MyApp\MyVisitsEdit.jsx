import React, { useEffect, useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import Input from "../Global/Input/Input";
import Button from "../Global/Button";
import DateTimeInput from "../Temporary/DateTimeInput"; // Adjust the import path as needed

const MyVisitsEdit = ({ data, onClose, onSave }) => {
  const {
    register,
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: data,
  });

  const [show, setShow] = useState(false);

  // Reset the form when data changes
  useEffect(() => {
    reset(data);
  }, [data, reset]);

  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const onSubmit = (formData) => {
    onSave(formData);
  };

  // Define non-date fields
  const visitFields = [
    { label: "Event Title", name: "title", type: "text" },
    { label: "Type", name: "type", type: "text" },
    { label: "Category", name: "category", type: "text" },
    { label: "Host", name: "host", type: "text" },
    { label: "Escort", name: "escort", type: "text" },
  ];

  return (
   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Edit Event</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        <form onSubmit={handleSubmit(onSubmit)} className="p-6">
          {/* Render the text fields */}
          {visitFields.map((field, idx) => (
            <div key={idx} className="flex items-center mb-4">
              <label className="w-1/4 text-[16px] font-normal">
                {field.label}
              </label>
              <div className="w-3/4">
                <Input
                  type={field.type}
                  {...register(field.name)}
                  className="w-full border rounded p-2"
                />
                {errors[field.name] && (
                  <p className="text-red-500 text-sm">
                    {errors[field.name].message}
                  </p>
                )}
              </div>
            </div>
          ))}

          {/* Render the Start Date & Time field using DateTimeInput */}
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal">
              Start Date & Time
            </label>
            <div className="w-3/4">
              <Controller
                name="startDate"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <DateTimeInput
                    value={value ? new Date(value) : null}
                    onChange={onChange}
                    error={errors.startDate && errors.startDate.message}
                  />
                )}
              />
            </div>
          </div>

          {/* Render the End Date & Time field using DateTimeInput */}
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal">
              End Date & Time
            </label>
            <div className="w-3/4">
              <Controller
                name="endDate"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <DateTimeInput
                    value={value ? new Date(value) : null}
                    onChange={onChange}
                    error={errors.endDate && errors.endDate.message}
                  />
                )}
              />
            </div>
          </div>

          <div className="flex gap-4 justify-center mt-4">
            <Button
              type="button"
              label="Cancel"
              onClick={onClose}
              className="px-4 py-2 bg-[#979797] text-white rounded"
            />
            <Button
              type="submit"
              label="Save"
              className="px-4 py-2 bg-[#4F2683] text-white rounded"
            />
          </div>
        </form>
      </div>
      </div>
      </div>
    </div>
  );
};

export default MyVisitsEdit;
