import React, { useState } from "react";
import { IoFilter } from "react-icons/io5";
import { FaEdit } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import FilterPanel from "../../../Components/Observation/FilterPanel";
import Button from "../../../Components/Global/Button";
import GenericTable, { FilterButtons } from "../../../Components/GenericTable";

const userData = [
  { id: 1, name: "<PERSON>", email: "<EMAIL>", phone: "1234567890", username: "<PERSON>", status: "Active" },
  { id: 2, name: "<PERSON>", email: "<EMAIL>", phone: "1234567890", username: "<PERSON>", status: "Expired" },
  { id: 3, name: "<PERSON>", email: "<EMAIL>", phone: "1234567890", username: "<PERSON>", status: "Active" },
  { id: 4, name: "<PERSON>", email: "<EMAIL>", phone: "1234567890", username: "<PERSON>", status: "Active" },
  { id: 5, name: "<PERSON>", email: "<EMAIL>", phone: "1234567890", username: "Mohan", status: "Expired" },
];

const Input = ({ type, placeholder, value, onChange, className }) => (
  <input
    type={type}
    placeholder={placeholder}
    value={value}
    onChange={onChange}
    className={`p-2 shadow-lg rounded border-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-700 ${className}`}
  />
);

const User = () => {
  const [filter, setFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [hoveredRowId, setHoveredRowId] = useState(null);
  const navigate = useNavigate();

  // Filter userData based on search term and status filter
  const filteredData = userData.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.phone.includes(searchTerm) ||
      user.username.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filter === "All" ? true : user.status.toLowerCase() === "active";
    return matchesSearch && matchesFilter;
  });

  const columns = [
    {
      name: "Name",
      selector: (row) => row.name,
      sortable: true,
      cell: (row) => (
        <div
          className="flex items-center space-x-3 relative group"
          onMouseEnter={() => setHoveredRowId(row.id)}
          onMouseLeave={() => setHoveredRowId(null)}
        >
          <span>{row.name}</span>
          {hoveredRowId === row.id && (
            <FaEdit
              className="text-blue-500 cursor-pointer ml-2"
              onClick={() => navigate(`/user-details`)}

            />
          )}
        </div>
      ),
    },
    { name: "Email", selector: (row) => row.email, sortable: true },
    { name: "Phone Number", selector: (row) => row.phone },
    { name: "User Name", selector: (row) => row.username },
    { name: "Status", selector: (row) => row.status },
  ];

  const handleAddUser = () => {
    // Navigate to add user form (update the route as necessary)
    navigate("/add-user-form");
  };

  return (
    <div className="flex flex-col px-8 py-4 pl-20 pt-20">
       <div className="mb-6">
        <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">Users</h2>
      </div>

      {/* Filter Buttons similar to Observation */}
      <div className="mb-4">
        <FilterButtons
          filter={filter}
          onFilterChange={setFilter}
          filterOptions={[
            { label: "All", value: "All" },
            { label: "Active", value: "Active" },
          ]}
        />
      </div>

      <GenericTable
        title="Users"
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        onAdd={handleAddUser}
        extraControls={
          <IoFilter
            className="bg-white shadow-sm border items-center p-[5px] text-[#4F2683] h-[32px] w-8 rounded cursor-pointer"
            onClick={() => setIsFilterPanelOpen(true)}
          />
        }
        columns={columns}
        data={filteredData}
        fixedHeader
        fixedHeaderScrollHeight="440px"
      />

      {isFilterPanelOpen && (
        <FilterPanel
          isOpen={isFilterPanelOpen}
          onClose={() => setIsFilterPanelOpen(false)}
          onApplyFilters={(filters) => {
            console.log("Applied Filters:", filters);
            setIsFilterPanelOpen(false);
          }}
        />
      )}
    </div>
  );
};

export default User;
