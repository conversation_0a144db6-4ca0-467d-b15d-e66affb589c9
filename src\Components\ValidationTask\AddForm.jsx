import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import Input from "../Global/Input/Input";
import CustomDropdown from "../Global/CustomDropdown";

const AddConfigurationForm = ({ onClose, onAddConfiguration }) => {
  // const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    title: "",
    status: "",
    type: "",
    // Assinee
    owner: "",
    description: "",
    startDate: "",
    reccurence: "",
    record: "",
    // Second Screen fields
    closesOn: "",
    validationReminder: "",
    template: "",
    to: "",
    cc: "",
    finalReminder: "",
    template2: "",
    to2: "",
    cc2: "",
    lapseAlert: "",
    template3: "",
    to3: "",
    cc3: "",
  });
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Update form data on input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Update form data on dropdown change
  const handleDropdownChange = (fieldName, value) => {
    setFormData((prev) => ({ ...prev, [fieldName]: value }));
  };

  // Basic validation for step 1
  const validateStepOne = () => {
    if (!formData.title.trim()) {
      toast.error("Please enter the Title");
      return false;
    }
    return true;
  };

  // Move to step 2 if validation passes
  const handleNext = (e) => {
    if (e) e.preventDefault(); // Prevent default form submission behavior
    if (!validateStepOne()) return;
    setCurrentStep(2);
  };

  // On form save, create the new configuration object and pass it to the parent
  const handleSave = (e) => {
    e.preventDefault();
    const currentDateTime = new Date().toLocaleString();
    const newConfiguration = {
      id: Date.now(),
      ...formData,
      creation: currentDateTime,
    };

    console.log("Final formData before save:", newConfiguration);

    toast.success("Configuration added successfully!", {
      autoClose: 1500,
      pauseOnHover: false,
    });

    // Use the callback to update the parent's table data
    setTimeout(() => {
      if (onAddConfiguration) {
        onAddConfiguration(newConfiguration);
      }
      onClose();
    }, 1500);
  };

  const steps = [
    { step: 1, label: "Configuration" },
    { step: 2, label: "Messaging" },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Validation Configuration</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

            {/* Step Tabs */}
            <div className="flex items-center mt-4 space-x-8">
              {steps.map((item) => (
                <button
                  key={item.step}
                  type="button"
                  onClick={() => setCurrentStep(item.step)}
                  className={`p-2 border items-center rounded-full ${currentStep === item.step
                    ? "bg-[#4F2683] border-[#4f2683] text-[#fff]"
                    : "bg-transparent border-[#4f2683] text-gray-500"
                    }`}
                >
                  {item.label}
                </button>
              ))}
            </div>
            {/* Form Content */}
            <div className="p-6">
              <form className="space-y-4" onSubmit={handleSave}>
                {/* STEP 1: Configuration Info */}
                {currentStep === 1 && (
                  <div className="w-full">
                    <div className="flex flex-col gap-4">
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Title</label>
                        <Input
                          type="text"
                          name="title"
                          value={formData.title}
                          onChange={handleInputChange}
                          placeholder="Enter Title"
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Validation Status</label>
                        <CustomDropdown
                          options={["Active", "Pending"]}
                          value={formData.status}
                          placeholder="Select Status"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("status", value)}
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Validation Type</label>
                        <CustomDropdown
                          options={["Access Area", "Area Owner", "Another"]}
                          value={formData.type}
                          placeholder="Select Type"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("type", value)}
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Assignee</label>
                        <CustomDropdown
                          options={["Owner Only", "User", "Another"]}
                          value={formData.owner}
                          placeholder="Select Assignee"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("owner", value)}
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Validation Scope/Description</label>
                        <Input
                          type="text"
                          name="description"
                          value={formData.description}
                          onChange={handleInputChange}
                          placeholder="Monthly Validation For Access Levels"
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Start Date</label>
                        <Input
                          type="date"
                          name="startDate"
                          value={formData.startDate}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Recurrence</label>
                        <CustomDropdown
                          options={["Every Month", "Every Quarter", "Every Year"]}
                          value={formData.reccurence}
                          placeholder="Select Recurrence"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("reccurence", value)}
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Record</label>
                        <Input
                          type="text"
                          name="record"
                          value={formData.record}
                          onChange={handleInputChange}
                          placeholder="Enter Record"
                        />
                      </div>
                    </div>
                  </div>
                )}
                {/* STEP 2: Messaging Info */}
                {currentStep === 2 && (
                  <div className="w-full">
                    <div className="flex flex-col gap-4">
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Closes On</label>
                        <Input
                          type="date"
                          name="closesOn"
                          value={formData.closesOn}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div className="flex space-x-4 mt-2">
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            className="form-checkbox"
                          />
                          <span>Retain All</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            className="form-checkbox"
                          />
                          <span>Revoke All</span>
                        </label>
                      </div>
                      <h2 className="text-[20px] font-normal">Notify Reviewer</h2>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Validation Reminder</label>
                        <Input
                          type="text"
                          name="validationReminder"
                          value={formData.validationReminder}
                          onChange={handleInputChange}
                          placeholder="Enter Validation Reminder"
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Template</label>
                        <CustomDropdown
                          options={["Template 1", "Template 2", "Template 3"]}
                          value={formData.template}
                          placeholder="Select Template"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("template", value)}
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">To</label>
                        <CustomDropdown
                          options={["User 1", "User 2", "User 3"]}
                          value={formData.to}
                          placeholder="Select Recipient"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("to", value)}
                        />
                      </div>
                      <div className="flex items-center gap-4 mb-8">
                        <label className="w-1/3">CC</label>
                        <CustomDropdown
                          options={["User 1", "User 2", "User 3"]}
                          value={formData.cc}
                          placeholder="Select An Option"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("cc", value)}
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Final Reminder</label>
                        <Input
                          type="text"
                          name="finalReminder"
                          value={formData.finalReminder}
                          onChange={handleInputChange}
                          placeholder="Enter Final Reminder"
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Template </label>
                        <CustomDropdown
                          options={["Template 1", "Template 2", "Template 3"]}
                          value={formData.template2}
                          placeholder="Select Template"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) =>
                            handleDropdownChange("template2", value)
                          }
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">To</label>
                        <CustomDropdown
                          options={["User 1", "User 2", "User 3"]}
                          value={formData.to2}
                          placeholder="Select Recipient"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("to2", value)}
                        />
                      </div>
                      <div className="flex items-center gap-4 mb-8">
                        <label className="w-1/3">CC</label>
                        <CustomDropdown
                          options={["User 1", "User 2", "User 3"]}
                          value={formData.cc2}
                          placeholder="Select An Option"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("cc2", value)}
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Lapse Alert</label>
                        <Input
                          type="text"
                          name="lapseAlert"
                          value={formData.lapseAlert}
                          onChange={handleInputChange}
                          placeholder="Enter Lapse Alert"
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">Template</label>
                        <CustomDropdown
                          options={["Template 1", "Template 2", "Template 3"]}
                          value={formData.template3}
                          placeholder="Select Template"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("template3", value)}
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">To</label>
                        <CustomDropdown
                          options={["User 1", "User 2", "User 3"]}
                          value={formData.to3}
                          placeholder="Select Recipient"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("to3", value)}
                        />
                      </div>
                      <div className="flex items-center gap-4">
                        <label className="w-1/3">CC 3</label>
                        <CustomDropdown
                          options={["User 1", "User 2", "User 3"]}
                          value={formData.cc3}
                          placeholder="Select An Option"
                          rounded="rounded-md"
                          bgColor="bg-white"
                          textColor="text-gray-700"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-11"
                          onSelect={(value) => handleDropdownChange("cc3", value)}
                        />
                      </div>
                    </div>
                  </div>
                )}
                {/* Navigation Buttons */}
                <div className="flex justify-center gap-2 mt-6">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 bg-gray-400 text-white rounded"
                  >
                    Cancel
                  </button>
                  {currentStep === 1 ? (
                    <button
                      type="button" // Ensure this is type="button"
                      onClick={handleNext}
                      className="px-4 py-2 bg-[#4F2683] text-white rounded"
                    >
                      Next
                    </button>
                  ) : (
                    <button type="submit"
                      className="px-4 py-2 bg-[#4F2683] text-white rounded">
                      Save
                    </button>
                  )}
                </div>
              </form>
            </div>
          </div>
          </div>
          </div>
        </div>
        );
};

        export default AddConfigurationForm;
