import React, { useEffect, useState } from "react";
import { FaUser } from "react-icons/fa";
import logos from "../../../Images/logowhite.svg";
import { useLocation } from "react-router-dom";
import CustomDropdown from "../CustomDropdown";
import { useDispatch } from "react-redux";
import { setSelectedFacility, setFacilities } from "../../../redux/facilitySlice";
import { getFacilities } from "../../../api/global";
import help from '../../../Images/Help.svg';
import notifition from '../../../Images/Notifition.svg'
import Help from "../../Help/Help";
import Notifition from "../../Notifition/Notifition";

const Header = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const [facilities, setFacilitiesState] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedFacility, setSelectedFaciliity] = useState('')
  const [showHelp, setShowHelp] = useState(false);
  const [showNotifition, setShowNotifition] = useState(false);

  useEffect(() => {
    const fetchFacilities = async () => {
      try {
        const response = await getFacilities();
        const facilitiesData = response.data?.data || [];
        setFacilitiesState(facilitiesData);
        dispatch(setFacilities(facilitiesData));

        const justLoggedOut = sessionStorage.getItem("justLoggedOut") === "true";
        sessionStorage.removeItem("justLoggedOut");

        if (!justLoggedOut) {
          // only auto‑select if NOT coming right from a logout
          const savedFacilityId = localStorage.getItem("selectedFacility");
          const facility =
            (savedFacilityId && facilitiesData.find(f => f.facility_id === savedFacilityId)) ||
            facilitiesData[0];
          if (facility) {
            setSelectedFaciliity(facility.name);
            dispatch(
              setSelectedFacility({ id: facility.facility_id, name: facility.name })
            );
          }
        }
      } catch (error) {
        console.error("Error fetching facilities:", error);
        setFacilitiesState([]);
      } finally {
        setLoading(false);
      }
    };

    fetchFacilities();
  }, [dispatch]);
  console.log("🚀 ~ Header ~ selectedFacility:", selectedFacility)

  const formatPathname = (path) => {
    const segments = path.split("/").filter(Boolean);
    const mainSegment = segments[0] || "Reception Desk";
    return mainSegment
      .replace(/-/g, " ")
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const pageTitle = formatPathname(location.pathname);

  const specialPages = [
    "/",
    "/doctors-appointment",
    "/temporary-cards",
    "/credential",
    "/credential-details",
  ];
  const showDropdown = specialPages.includes(location.pathname);

  const handleFacilitySelect = (selectedFacilityName) => {
    const facility = facilities.find(f => f.name === selectedFacilityName);
    // console.log("🚀 ~ handleFacilitySelect ~ facility:", facility)


    if (facility) {
      dispatch(setSelectedFacility({ id: facility.facility_id, name: facility.name }));
      localStorage.setItem("selectedFacility", facility.facility_id);
      setSelectedFaciliity(facility.name);
    } else {
      console.error("Selected facility not found:", selectedFacilityName);
    }
  };

  // useEffect(() => {
  // // localStorage.removeItem("selectedFacility");

  // },[])


  return (
    <>
      {/* Help Modal */}
      {showHelp && <Help isOpen={showHelp} onClose={() => setShowHelp(false)} />}
      {/* Notifition Modal */}
      {showNotifition && <Notifition isOpen={showNotifition} onClose={() => setShowNotifition(false)} />}
      <div className="navbar bg-[#FFFFFFF2] shadow-[0px_8px_14px_0px_#00000036] flex fixed top-0 w-full items-center text-[#0B0B0B] text-opacity-[0.46]
 p-3 z-10 transition-all duration-300 h-14">
        {/* Left Section */}
        <div className="pr-10 pl-3">
          <img src={logos} alt="logo" className="h-8 w-auto" />
        </div>

        <div className="flex flex-col md:flex-row items-center gap-3 md:gap-5 w-full">
          {/* Reception Desk and User Icon */}
          <div className="flex items-center justify-between w-full md:w-auto gap-3">
            <h1 className="font-poppins text-lg md:text-lg">{pageTitle}</h1>
            <FaUser className="text-white md:hidden text-lg" />
          </div>

          {/* Conditionally Render Dropdown */}
          {showDropdown && (
            <div className="relative w-full md:w-[300px]">
              {loading ? (
                <div className="text-sm">Loading facilities...</div>
              ) : (
                <CustomDropdown
                  defaultValue={selectedFacility || "select facility"} // Use selectedFacility or default text
                  options={facilities.map(facility => facility.name)} // Pass facility names as options
                  onSelect={handleFacilitySelect} // Handle facility selection
                  bgColor="bg-[#fffffff2]"
                  textColor="text-[#0B0B0B]"
                  hoverBgColor="hover:bg-[#4F2683]"
                  borderColor="border-gray-300"
                  className="rounded-full truncate max-w-[200px] text-opacity-[0.46] overflow-hidden whitespace-nowrap"
                />
              )}

            </div>

          )}
        </div>
        <div className="flex items-center gap-4 ml-auto pr-4">
          <img src={help} alt="help" className="h-6 w-6 cursor-pointer" onClick={() => setShowHelp(true)} />
          <img src={notifition} alt="notification" className="h-6 w-6 cursor-pointer" onClick={() => setShowNotifition(true)} />
        </div>
      </div>
    </>
  );
};

export default Header;