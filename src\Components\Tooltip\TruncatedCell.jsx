
import React, {useEffect , useRef, useState } from "react";
import { Tooltip } from "react-tooltip";

const TruncatedCell = ({ text }) => {
    const cellRef = useRef(null);
    const [isOverflowing, setIsOverflowing] = useState(false);
  
  
    useEffect(() => {
      if (cellRef.current) {
        setIsOverflowing(cellRef.current.scrollWidth > cellRef.current.clientWidth);
      }
    }, [text]);
  
    return (
      <div
        ref={cellRef}
        className="truncate max-w-[120px] overflow-hidden whitespace-nowrap"
        data-tooltip-id={isOverflowing ? `tooltip-${text}` : undefined}
        data-tooltip-content={isOverflowing ? text : undefined}
      >
        {text}
        {isOverflowing &&
          <div className="fixed">
            <Tooltip id={`tooltip-${text}`} className=" !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium" place="bottom" effect="solid" />
          </div>
        }
      </div>
    );
  };

  export default TruncatedCell