import React, { useState } from "react";
import Button from "../Global/Button";

const AssignedIdentitiesAdd = ({ onSubmit, onClose, availableIdentities }) => {
  // Default sample assigned identities (6–8 entries)
  const defaultIdentities = [
    {
      name: "<PERSON>",
      eid: "E001",
      type: "Employee",
      company: "Company A",
      organization: "Org1",
      jobTitle: "Developer",
      endDate: "2023-12-31",
      status: "Active",
    },
    {
      name: "<PERSON>",
      eid: "E002",
      type: "Manager",
      company: "Company B",
      organization: "Org2",
      jobTitle: "Team Lead",
      endDate: "2023-11-30",
      status: "Active",
    },
    {
      name: "<PERSON>",
      eid: "E003",
      type: "Employee",
      company: "Company C",
      organization: "Org3",
      jobTitle: "Designer",
      endDate: "2023-10-15",
      status: "Inactive",
    },
    {
      name: "<PERSON>",
      eid: "E004",
      type: "Director",
      company: "Company D",
      organization: "Org4",
      jobTitle: "Project Manager",
      endDate: "2023-09-30",
      status: "Active",
    },
    {
      name: "<PERSON>",
      eid: "E005",
      type: "Employee",
      company: "Company E",
      organization: "Org5",
      jobTitle: "QA Engineer",
      endDate: "2023-08-31",
      status: "Active",
    },
    {
      name: "Fiona Gallagher",
      eid: "E006",
      type: "Manager",
      company: "Company F",
      organization: "Org6",
      jobTitle: "HR Manager",
      endDate: "2023-07-31",
      status: "Inactive",
    },
    {
      name: "George Clooney",
      eid: "E007",
      type: "Employee",
      company: "Company G",
      organization: "Org7",
      jobTitle: "Support",
      endDate: "2023-06-30",
      status: "Active",
    },
    {
      name: "Hannah Montana",
      eid: "E008",
      type: "Employee",
      company: "Company H",
      organization: "Org8",
      jobTitle: "Sales",
      endDate: "2023-05-31",
      status: "Active",
    },
  ];

  // Use availableIdentities if provided; otherwise, use defaultIdentities.
  const identitiesList =
    availableIdentities && availableIdentities.length
      ? availableIdentities
      : defaultIdentities;

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedIdentity, setSelectedIdentity] = useState("");
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [show, setShow] = useState(false);

  // Smooth open animation
  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 50);
    return () => clearTimeout(timer);
  }, []);

  // Filter identities based on the search term.
  const filteredIdentities = identitiesList.filter((identity) =>
    identity.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectIdentity = (name) => {
    setSelectedIdentity(name);
    setSearchTerm(name);
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedIdentity) {
      alert("Please select an identity.");
      return;
    }
    const selectedIdentityObj = identitiesList.find(
      (identity) => identity.name === selectedIdentity
    );
    onSubmit(selectedIdentityObj);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-bold text-[#4F2683]">Add Assigned Identity</h2>
          <button
            type="button"
            className="w-8 h-8 bg-[#4F2683] text-white flex items-center justify-center rounded-full text-2xl"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">
            {/* Form */}
            <form onSubmit={handleSubmit} className="bg-white p-6 pt-2 rounded-lg my-3">
              {/* Search Input with Dropdown */}
              <div className="mb-4 flex items-center">
                <label className="text-[16px] font-normal w-1/4">Select Identity</label>
                <div className="relative w-3/4">
                  <input
                    type="text"
                    placeholder="Search Identity"
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      setIsDropdownVisible(true);
                    }}
                    onFocus={() => setIsDropdownVisible(true)}
                    onBlur={() => setTimeout(() => setIsDropdownVisible(false), 150)}
                    className="w-full h-11 border border-gray-300 rounded px-3"
                  />
                  {isDropdownVisible && (
                    <div className="absolute top-full left-0 w-full mt-1 border bg-white rounded-md shadow-lg max-h-60 overflow-y-auto z-50">
                      {filteredIdentities.length > 0 ? (
                        filteredIdentities.map((identity) => (
                          <div
                            key={identity.name}
                            className="p-2 cursor-pointer hover:bg-gray-100"
                            onMouseDown={() => handleSelectIdentity(identity.name)}
                          >
                            {identity.name}
                          </div>
                        ))
                      ) : (
                        <div className="p-2 text-gray-700 text-center">No Results Found.</div>
                      )}
                    </div>
                  )}
                </div>
              </div>
              {/* Action Buttons */}
              <div className="flex gap-4 justify-center">
                <Button type="button" label="Cancel" onClick={() => {
                  setShow(false);
                  setTimeout(onClose, 700);
                }} className="bg-gray-400 text-white" />
                <Button type="submit" label="Add" className="bg-[#4F2683] text-white" />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssignedIdentitiesAdd;
