import React, { useState } from "react";
import EditableSection from "../../Global/EditabelForIdentity";
import GenericTable from "../../GenericTable";
import TruncatedCell from "../../Tooltip/TruncatedCell";
import TruncatedRow from "../../Tooltip/TrucantedRow";

const ViewRequest = () => {
  // Restructure data to include label and value for each key
  const [requestInfo, setRequestInfo] = useState({
    RequestID: { label: "Request ID", value: "17810919" },
    RequestType: { label: "Request Type", value: "Card Change" },
    RequestedDate: { label: "Requested Date", value: "19-Mar-2025 | 11:46 PM" },
    RequestedBy: { label: "Requested By", value: "GANGCAI HU, 893859003" },
    SLA: { label: "SLA", value: "0hrs | 0%" },
    Justification: { label: "Justification", value: "It was damaged. Need a new card" },
    // Example: if Status is editable via dropdown
    Status: { label: "Status", value: "Active" },
  });

  const [cardShipment, setCardShipment] = useState({
    ShipTo: { label: "Ship To", value: "GANGCAI HU" },
    OffsiteShipping: { label: "Offsite Shipping", value: "Offsite Shipping" },
    Expedite: { label: "Expedite", value: "No" },
    Carrier: { label: "Carrier", value: "Carrier" },
    Account: { label: "Account", value: "0hrs | 0%" },
    AccountPostalCode: { label: "Account Postal Code", value: "123456" },
    Address: { label: "Address", value: "80/2 patel marg manasrovar jaipur" },
  });

  const handleInputChange = (section, key, value) => {
    if (section === "requestInfo") {
      setRequestInfo((prev) => ({
        ...prev,
        [key]: { ...prev[key], value },
      }));
    } else if (section === "cardShipment") {
      setCardShipment((prev) => ({
        ...prev,
        [key]: { ...prev[key], value },
      }));
    }
  };

  const statusOptions = [
    { label: "Active", value: "Active" },
    { label: "Inactive", value: "Inactive" },
  ];

  // Table columns for "Identity" table
  const approvalsColumns = [
    {
      name: <TruncatedCell text="Name" />,
      selector: (row) => row.approvalStep,
      cell: (row) => <TruncatedRow text={row.approvalStep} />,
      sortable: true,
    },
    {
      name: "EID",
      selector: (row) => row.stepId,
      cell: (row) => <TruncatedRow text={row.stepId} />,
    },
    {
      name: <TruncatedCell text="Company" />,
      selector: (row) => row.approver,
      cell: (row) => <TruncatedRow text={row.approver} />,
    },
    {
      name: <TruncatedCell text="Job Title" />,
      selector: (row) => row.role,
      cell: (row) => <TruncatedRow text={row.role} />,
    },
    {
      name: <TruncatedCell text="Type" />,
      selector: (row) => row.comments,
      cell: (row) => <TruncatedRow text={row.comments} />,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      sortable: true,
      cell: (row) => (
        <span
          className={`w-24 py-1 items-center flex justify-center text-sm font-semibold rounded-full ${
            row.status.toLowerCase() === "active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
  ];

  const identityData = [
    {
      approvalStep: "Nema",
      stepId: "2333319",
      approver: "Rakop Supervisor",
      role: "Supervisor",
      date: "19-Mar-2025",
      comments: "Credentials pulled up for recheck",
      status: "Active",
    },
    {
      approvalStep: "Darshil",
      stepId: "2333320",
      approver: "John Manager",
      role: "Manager",
      date: "20-Mar-2025",
      comments: "Everything looks good",
      status: "Inactive",
    },
  ];

  // Table columns for "Approvals" table
  const approvalColumn = [
    {
      name: <TruncatedCell text="Approval Step" />,
      selector: (row) => row.assignedTo,
      cell: (row) => <TruncatedRow text={row.assignedTo} />,
    },
    {
      name: <TruncatedCell text="Task ID" />,
      selector: (row) => row.taskId,
      cell: (row) => <TruncatedRow text={row.taskId} />,
    },
    {
      name: <TruncatedCell text="Task Owner" />,
      selector: (row) => row.taskName,
      cell: (row) => <TruncatedRow text={row.taskName} />,
    },
    {
      name: <TruncatedCell text="Approver" />,
      selector: (row) => row.role,
      cell: (row) => <TruncatedRow text={row.role} />,
    },
    {
      name: <TruncatedCell text="Response Date" />,
      selector: (row) => row.dueDate,
      cell: (row) => <TruncatedRow text={row.dueDate} />,
    },
    {
      name: <TruncatedCell text="Comments" />,
      selector: (row) => row.comments,
      cell: (row) => <TruncatedRow text={row.comments} />,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-24 py-1 items-center flex justify-center text-sm font-semibold rounded-full ${
            row.status.toLowerCase() === "completed"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
  ];

  const approvalData = [
    {
      taskId: "T-101",
      taskName: "Review Application",
      assignedTo: "A. Sharma",
      role: "Reviewer",
      dueDate: "21-Mar-2025",
      comments: "Pending final check",
      status: "Pending",
    },
    {
      taskId: "T-102",
      taskName: "Print ID Card",
      assignedTo: "B. Verma",
      role: "Printer",
      dueDate: "22-Mar-2025",
      comments: "Waiting on design approval",
      status: "Completed",
    },
  ];

  return (
    <div className="p-4">
      <EditableSection
        title="Request Information"
        data={requestInfo}
        dropdownKeys={["Status"]}
        dropDownclassName="text-[#696969]"
        dropdownOptions={{
          Status: statusOptions,
        }}
        onChange={(key, value) => handleInputChange("requestInfo", key, value)}
      />

      <EditableSection
        title="Card Shipment"
        data={cardShipment}
        onChange={(key, value) => handleInputChange("cardShipment", key, value)}
      />

      <div className="mt-8 bg-white shadow-md rounded-lg">
        <GenericTable
          title="Identity"
          columns={approvalsColumns}
          data={identityData}
          showSearch={false}
          showAddButton={false}
        />
      </div>

      <div className="mt-8 bg-white shadow-md rounded-lg">
        <GenericTable
          title="Approvals"
          columns={approvalColumn}
          data={approvalData}
          showSearch={false}
          showAddButton={false}
        />
      </div>
    </div>
  );
};

export default ViewRequest;
