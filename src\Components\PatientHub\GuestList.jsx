import React, { useState, useEffect } from 'react';
import GenericTable from ".././GenericTable";
import { getPatientGuests } from '../../api/PatientHub';
import TruncatedCell from '../Tooltip/TruncatedCell';
import TruncatedRow from '../Tooltip/TrucantedRow';

const GuestList = ({ patientId }) => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [activeFilter, setActiveFilter] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('first_name'); // Default sort column
  const [sortOrder, setSortOrder] = useState('ASC'); // Default sort order
  
  useEffect(() => {
    const fetchGuests = async () => {
      try {
        if (!patientId) {
          console.error("Patient ID is required to fetch guest data.");
          return;
        }
        const guests = await getPatientGuests({
          patient_id: patientId,
          search: searchQuery, // Pass the search query parameter
          sortBy, // Pass the sortBy parameter
          sortOrder, // Pass the sortOrder parameter
        });
        const formattedGuests = guests.map(guest => ({
          visitorsName: guest.guest_full_name,
          checkIn: guest.guest_arrival_time,
          checkOut: guest.guest_departure_time,
          facility: guest.facility_name,
          building: guest.building_name,
          floor: guest.floor_number,
          room: guest.room_number,
          status: guest.appointment_guest_status_name,
        }));
        setData(formattedGuests);
        setFilteredData(formattedGuests);
      } catch (error) {
        console.error("Error fetching guest data:", error);
      }
    };

    fetchGuests();
  }, [patientId,searchQuery, sortBy, sortOrder]); // Re-fetch data when sortBy, sortOrder, or searchQuery changes

  const columns = [
    {
      id: 'first_name',
      name: <TruncatedCell text='Visitor Name' />,
      selector: row => row.visitorsName,
      sortable: true,
    },
    {
      id: 'checkIn',
      name: <TruncatedCell text='Check In Date & Time' />,
      selector: row => row.checkIn,
      cell: row => <TruncatedRow text={row.checkIn} />,
      sortable: true,
    },
    {
      id: 'checkOut',
      name: <TruncatedCell text='Check Out Date & Time' />,
      selector: row => row.checkOut,
      cell: row => <TruncatedRow text={row.checkOut} />,
      sortable: true,
    },
    {
      id: 'facility_name',
      name: 'Facility',
      selector: row => row.facility,
      cell: row => <TruncatedRow text={row.facility} />,
      sortable: true,
    },
    {
      id: 'building_name',
      name: 'Building',
      selector: row => row.building,
      cell: row => <TruncatedRow text={row.building} />,
      sortable: true,
    },
    {
      id: 'floor_number',
      name: 'Floor',
      selector: row => row.floor,
      cell: row => <TruncatedRow text={row.floor} />,
      sortable: true,
    },
    {
      id: 'room_number',
      name: 'Room',
      selector: row => row.room,
      cell: row => <TruncatedRow text={row.room} />,
      sortable: true,
    },
    {
      id: 'appointment_guest_status_name',
      name: 'Status',
      selector: row => row.status,
      sortable: true,
    },
  ];

  const handleSortClick = (column, sortDirection) => {
    setSortBy(column.id); // Set the column to sort by
    setSortOrder(sortDirection.toUpperCase()); // Set the sort order (ASC/DESC)
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value); // Update the search query
  };

  return (
    <div>
      <div className='my-6 gap-3 flex items-center'>
        <button
          onClick={() => setFilteredData(data)}
          className={`h-10 w-20 rounded-full ${activeFilter === 'All' ? 'bg-[#4F2683] hover:bg-[#6A3BAA] text-white' : 'border border-[#4F2683] text-[#4F2683] hover:bg-blue-100'}`}
        >
          All
        </button>
        <button
          onClick={() => setFilteredData(data.filter(item => item.status === 'Checked In'))}
          className={`h-10 w-28 px-2 rounded-full ${activeFilter === 'Check In' ? 'bg-[#4F2683] hover:bg-[#6A3BAA] text-white' : 'border border-[#4F2683] text-[#4F2683] hover:bg-blue-100'}`}
        >
          Check In
        </button>
        <button
          onClick={() => setFilteredData(data.filter(item => item.status === 'Checked Out'))}
          className={`h-10 w-28 px-2 rounded-full ${activeFilter === 'Check Out' ? 'bg-[#4F2683] hover:bg-[#6A3BAA] text-white' : 'border border-[#4F2683] text-[#4F2683] hover:bg-blue-100'}`}
        >
          Check Out
        </button>
      </div>

      <div className='bg-white rounded-[10px]'>
        <GenericTable
          title="Guest(s)"
          searchTerm={searchQuery}
          onSearchChange={handleSearch} // Pass the search handler
          onSort={handleSortClick} // Pass the sorting handler
          columns={columns}
          data={filteredData}
          showSearch={true}
          showAddButton={false}
        />
      </div>
    </div>
  );
};

export default GuestList;