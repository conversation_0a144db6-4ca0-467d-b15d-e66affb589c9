import React from 'react';
import { Link, useLocation } from "react-router-dom";

const TabsComponent = () => {
  const location = useLocation();

  return (
    <div className="flex justify-center items-center gap-4 mt-6 pt-14 flex-wrap">
      <Link
        to="/"
        className={`flex items-center rounded-full border-2 min-w-[280px] h-[50px] font-[400] leading-[23px] text-center ${
          location.pathname === "/"
            ? 'bg-[#4F2683] text-white border-[#4F2683]'
            : 'bg-white text-[#4F2683] border-[#4F2683]'
        }`}
      >
        <button
          className={`w-full h-full rounded-full text-sm font-medium flex items-center justify-center gap-2 ${
            location.pathname === "/" ? 'filter brightness-0 invert' : 'filter brightness-50'
          }`}
        >
          <h3 className='text-[20px] font-normal leading-[23px] text-center'>
            Inpatient Visit
          </h3>
        </button>
      </Link>

      <Link
        to="/doctors-appointment"
        className={`flex items-center rounded-full border-2 min-w-[280px] h-[50px] font-[400] font-poppins leading-[23px] text-center ${
          location.pathname === "/doctors-appointment"
            ? 'bg-[#4F2683] text-white border-[#4F2683]'
            : 'bg-white text-[#4F2683] border-[#4F2683]'
        }`}
      >
        <button
          className={`w-full h-full rounded-full text-sm font-medium flex items-center justify-center gap-2 ${
            location.pathname === "/doctors-appointment" ? 'filter brightness-0 invert' : 'filter brightness-50'
          }`}
        >
          <h3 className='text-[20px] font-normal leading-[23px] text-center'>
            Doctor's Appointment
          </h3>
        </button>
      </Link>
    </div>
  );
};

export default TabsComponent;