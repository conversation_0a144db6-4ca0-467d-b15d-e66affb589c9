import React, { useState } from "react";
import GenericTable from "../../../GenericTable";
import TabsWithTables from "./TabsWithTables"; // Import TabsWithTables

const ViewReport = ({ onClose }) => {
  const [showTabs, setShowTabs] = useState(false); // State to toggle TabsWithTables
  const [selectedRow, setSelectedRow] = useState(null); // State to store clicked row data

  const reportData = [
    { request: "Request ID", type: "12345", reason: "Request ID", createOn: "Request ID", startDate: "Request ID", endDate: "Request ID", status: "Request ID" },
   
  ];

  const handleRowClick = (row) => {
    setSelectedRow(row); // Store clicked row data
    setShowTabs(true); // Show TabsWithTables
  };

  const columns = [
    {
      name: "Request Id",
      selector: (row) => row.request,
      sortable: true,
      cell: (row) => (
        <button
          className="text-blue-500 underline"
          onClick={() => handleRowClick(row)}
        >
          {row.request}
        </button>
      ),
    },
    { name: "Type", selector: (row) => row.type },
    { name: "Reason", selector: (row) => row.reason },
    { name: "Created On", selector: (row) => row.createOn },
    { name: "Start Date", selector: (row) => row.startDate },
    { name: "End Date", selector: (row) => row.endDate },
    { name: "Status", selector: (row) => row.status },
  ];

  if (showTabs) {
    return <TabsWithTables onClose={() => setShowTabs(false)} />;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-4xl bg-white rounded-lg shadow-lg">
        {/* Header Section */}
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <h2 className="text-2xl font-semibold text-[#4F2683]">View Request List</h2>
          <button
            className="w-8 h-8 text-xl bg-[#4F2683] text-white rounded-full flex items-center justify-center"
            type="button"
            onClick={onClose}
          >
            &times;
          </button>
        </div>

        {/* Content Section */}
        <div className="bg-white">
          <GenericTable
            showAddButton={false}
            columns={columns}
            data={reportData}
            fixedHeader
            fixedHeaderScrollHeight="400px"
            highlightOnHover
            striped
          />
        </div>
      </div>
    </div>
  );
};

export default ViewReport;
