import React from "react";
import { IoMdAlert } from "react-icons/io";
import Button from "../../Components/Global/Button";
import formatDateTime from "../../utils/formatDate";

const getPatientColumns = ({ t, handlePatientClick }) => [
    {
        name: t('inpatient_visit.patient_name_column'),
        cell: (row) => (
            <span onClick={() => handlePatientClick(row)} className="cursor-pointer text-gray-700">
                {row.name}
            </span>
        ),
        sortable: true,
    },
    {
        name: t('inpatient_visit.guest_pin_column'),
        selector: (row) => row.GuestPin,
    },
    {
        name: t('inpatient_visit.dob_column'),
        selector: (row) => row.dob,
    },
    {
        name: t('inpatient_visit.patient_location_column'),
        selector: (row) => row.site,
    },
];

const getGuestColumns = ({
    t,
    demoimg,
    formatDateTime,
    handleScreeningToggle,
    handleAlertClick,
    openModal,
    handlePrintClick,
    handleCheckIn,
    handleCheckOut,
    <PERSON><PERSON>,
    Chat,
    Print,
    card,
    In,
    Out,
}) => [
        {
            id: 'first_name',
            name: t('inpatient_visit.guest_name_column'),
            cell: (row) => (
                <div className="flex items-center">
                    <img
                        src={row.image || demoimg}
                        alt={row.guestName}
                        className="w-8 h-8 rounded-full mr-2"
                    />
                    <span>{row.guestName}</span>
                </div>
            ),
            selector: (row) => row.guestName,
            width: "15%",
            sortable: true,
        },
        {
            name: t('inpatient_visit.screening_column'),
            cell: (row) => (
                <div className="flex items-center">
                    <Button
                        type="toggle"
                        initialState={!row.screening}
                        onClick={(state) => handleScreeningToggle(row.id, state)}
                        disabled={true}
                    />
                    {row.screening === 1 && (
                        <IoMdAlert
                            className="ml-2 text-red-500 cursor-pointer pulsating-icon"
                            title={t('inpatient_visit.screening_alert')}
                            size={20}
                            onClick={() => handleAlertClick(row)}
                        />
                    )}
                </div>
            ),
            width: "10%",
        },
        {
            id: 'friends_and_family',
            name: "Friends & Family",
            cell: row => <div>{row.friends_and_family}</div>,
            selector: row => row.friends_and_family,
            sortable: true,
            width: "15%",
        },
        {
            id: 'guest_pin',
            name: "Guest Pin",
            cell: (row) => <div>{row.guest_pin}</div>,
            selector: (row) => row.guest_pin,
            sortable: true,
            width: "11%",
        },
        {
            id: 'appointment_guest_status_name',
            name: "Status",
            cell: (row) =>  <div
                    className={`px-2 py-1 rounded-md text-sm font-medium
                ${row.status === "Checked-In"
                            ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
                            : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
                        }`}
                >
                    {row.status}
                </div>,
            selector: (row) => row.status,
            width: "14%",
            sortable: true,
        },
        {
            id: 'guest_arrival_time',
            name: "Arrival Time",
            cell: (row) => <div>{formatDateTime(row.arrivalTime)}</div>,
            selector: (row) => row.arrivalTime,
            width: "13%",
            sortable: true,
        },
        {
            name: "Action",
            cell: (row) => (
                <div className="flex justify-center items-center space-x-2">
                    <img
                        src={Cemara}
                        alt="Camera"
                        title="Capture Picture"
                        className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                        onClick={() => openModal("Update Profile Image", row.id)}
                    />
                    <img
                        src={Chat}
                        alt="Chat"
                        title="NDA"
                        className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    />
                    <img
                        src={Print}
                        alt="Print"
                        title="Print Guest Label"
                        className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                        onClick={() => handlePrintClick(row)}
                    />
                    <img
                        src={card}
                        alt="Card"
                        title="Guest Card"
                        className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    />
                    <img
                        src={In}
                        alt="Check In"
                        title="Guest Check-In"
                        className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                        onClick={() => handleCheckIn(row.appointment_guest_id)}
                    />
                    <img
                        src={Out}
                        alt="Out"
                        title="Guest Check-Out"
                        className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                        onClick={() => handleCheckOut(row.appointment_guest_id)}
                    />
                </div>
            ),
            center: true,
            sortable: false,
        },
    ];

const getProhibitedGuestColumns = ({ t }) => [
    {
        id: "first_name",
        name: "Name",
        selector: (row) =><span>{row.first_name} {row.last_name}</span>,
        sortable: true,
    },
    {
        id: "email",
        name: "Email",
        selector: (row) => row.email,
        sortable: true,
    },
    {
        id: "phone",
        name: "Phone",
        selector: (row) => row.phone,
        sortable: true,
    },
    {
        id: "reason",
        name: "Reason",
        selector: (row) => row.reason,
        sortable: true,
    },
    {
        id: "denied_on",
        name: "Denied On",
        selector: (row) => row.denied_on,
        sortable: true,
    },
    {
        id: "birth_date",
        name: "Birth Date",
        selector: (row) => row.birth_date ? formatDateTime(row.birth_date) : ''  ,
        sortable: true,
    },
];

export default {
    getPatientColumns,
    getGuestColumns,
    getProhibitedGuestColumns,
};
