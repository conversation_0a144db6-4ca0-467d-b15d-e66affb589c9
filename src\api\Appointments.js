import api from "./";

/**
 * Retrieves a paginated list of appointments.
 *
 * @param {object} params - Query parameters including page and limit.
 * @returns {Promise<any>} A promise that resolves to the response data containing paginated appointment details.
 */
export const getAppointments = async (params = {}) => {
  const response = await api.get("appointments/search", { params });
  return response.data;
};

/**
 * Searches today's appointments based on query parameters.
 *
 * @param {object} params - Query parameters including search and type.
 * @returns {Promise<any>} A promise that resolves to the list of today's appointments.
 */
export const searchAppointments = async (params = {}) => {
  const response = await api.get("appointments/search", { params });
  return response.data;
};

/**
 * Retrieves combined patient and appointment information.
 *
 * @param {object} params - Optional query parameters.
 * @returns {Promise<any>} A promise that resolves to the combined patient and appointment data.
 */
export const getPatientAppointments = async (params = {}) => {
  const response = await api.get("appointments/patient-appointments", { params });
  return response.data;
};

/**
 * Retrieves all patient appointments with detailed patient data.
 *
 * @param {object} params - Optional query parameters.
 * @returns {Promise<any>} A promise that resolves to the data of all appointments with patient details.
 */
export const getAllPatientAppointments = async (params = {}) => {
  const response = await api.get("appointments/all-patient-appointments", { params });
  return response.data;
};

/**
 * Retrieves appointment details by ID.
 *
 * @param {string} appointmentId - The unique identifier of the appointment.
 * @returns {Promise<any>} A promise that resolves to the appointment details.
 */
export const getAppointmentById = async (appointmentId) => {
  const response = await api.get(`appointments/${appointmentId}`);
  return response.data;
};

/**
 * Creates a new appointment.
 *
 * @param {object} appointmentData - The data for the new appointment including details such as date, time, participant info, etc.
 * @returns {Promise<any>} A promise that resolves to the response data containing the newly created appointment.
 */
export const createAppointment = async (appointmentData) => {
  const response = await api.post("appointments", appointmentData);
  return response.data;
};

/**
 * Updates appointment details.
 *
 * @param {string} appointmentId - The unique identifier of the appointment to update.
 * @param {object} appointmentData - The data to update. This may include properties such as date, time, participant info, status, etc.
 * @returns {Promise<any>} A promise that resolves to the response data containing the updated appointment.
 */
export const updateAppointment = async (appointmentId, appointmentData) => {
  const response = await api.patch(`appointments/${appointmentId}`, appointmentData);
  return response.data;
};

/**
 * Changes the status of an appointment.
 *
 * @param {string} appointmentId - The unique identifier of the appointment whose status is to be changed.
 * @param {object} statusData - An object containing the new status (e.g., { status: "Cancelled" }).
 * @returns {Promise<any>} A promise that resolves to the response data confirming the status update.
 */
export const updateAppointmentStatus = async (appointmentId, statusData) => {
  const response = await api.patch(`appointments/${appointmentId}/status`, statusData);
  return response.data;
};

/**
 * Creates a new guest.
 *
 * @param {object} guestData - The data of the guest to be created.
 * @returns {Promise<any>} A promise that resolves to the newly created guest data.
 */
export const createGuest = async (guestData, screening = 0) => {
  const response = await api.post("appointments/guests", {
    ...guestData,
    screening
  });
  return response.data.message.guest;
};
/**
 * Checks in a guest using their appointment_guest_id.
 *
 * @param {string|number} appointment_guest_id - The unique ID of the guest appointment.
 * @returns {Promise<any>} A promise that resolves to the check-in confirmation data.
 */
export const checkGuest = async (appointment_guest_id, action ) => {
  const response = await api.patch(`appointments/guests/${appointment_guest_id}/check?action=${action}&appointment_guest_id=${appointment_guest_id}`);
  return response.data;
};
/**
 * Checks in or checks out a patient for an appointment.
 *
 * @param {string|number} appointment_id - The unique ID of the appointment.
 * @param {string} action - The check-in/out action (e.g., 'check-in' or 'check-out').
 * @returns {Promise<any>} A promise that resolves to the confirmation data.
 */
export const checkAppointment = async (appointment_id, action) => {
  const response = await api.patch(`/appointments/${appointment_id}/check?action=${action}`);
  return response.data;
};

/**
 * Updates the image of a guest.
 *
 * @param {string} patientGuestId - The unique ID of the guest.
 * @param {FormData} imageData - A FormData object containing the image file.
 * @returns {Promise<any>} A promise that resolves to the updated image response or throws an error.
 */
export const updateGuestImage = async (patientGuestId, imageData) => {
  try {
    const response = await api.patch(`/guests/${patientGuestId}/image`, imageData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error updating guest image:", error);
    throw new Error(error.response?.data?.message || "Failed to update guest image.");
  }
};
/**
 * Fetches all patient appointment guests for a specific appointment.
 *
 * @param {string}  appointmentId               - The unique ID of the appointment.
 * @param {number}  type                        - Guest type (0 = regular, 2 = denied, etc.).
 * @param {string}  patientId                   - The unique ID of the patient.
 * @param {string}  sortBy                      - Column to sort by.
 * @param {string}  sortOrder                   - Sort direction ("ASC" or "DESC").
 * @param {string}  appointment_guest_status    - (Optional) Filter by guest status.
 * @param {string}  searchTerm                  - (Optional) Search query for server‐side filtering.
 * @returns {Promise<any[]>} A promise that resolves to the array of guest records.
 */
export const fetchAllGuests = async (
  appointmentId,
  type,
  patientId,
  sortBy,
  sortOrder,
  appointment_guest_status,
  searchTerm = ""
) => {
  const params = new URLSearchParams({
    guest_type: type,
    appointment_id: appointmentId,
    patient_id: patientId,
    ...(appointment_guest_status && { appointment_guest_status }),
    ...(sortBy && { sortBy }),
    ...(sortOrder && { sortOrder }),
    ...(searchTerm && { search: searchTerm })
  });

  const response = await api.get(`appointments/guests/view?${params.toString()}`);
  return response.data?.data?.data || [];
};

/**
 * Searches for guests based on a term and facility.
 *
 * @param {object}   params
 * @param {string}   params.search        – the guest name (or part of it) to search for
 * @param {string|number} params.facility_id – optional facility ID to scope the search
 * @returns {Promise<any>} A promise that resolves to the list of matching guests.
 */
export const searchGuests = async ({ search = "", facility_id="" } = {}) => {

  
  const response = await api.get("appointments/guests/search", {
    params: { search, facility_id }
  });
  return response.data;
};
// console.log("🚀 ~ searchGuests ~ facility_id:", facility_id)

/**
 * Retrieves denied guests for a specific patient, with optional server‐side search.
 * 
 * @param {string} patient_id    - The unique ID of the patient.
 * @param {number} guest_type    - Type of guest (e.g. 2 for denied guests).
 * @param {string} sortBy        - Column to sort by (e.g. "first_name").
 * @param {string} sortOrder     - Sort direction ("ASC" or "DESC").
 * @param {string} searchTerm    - (Optional) Search query for filtering denied guests.
 * @returns {Promise<any>}       A promise that resolves to the API response.
 */
export const getDeniedGuests = async (
  patient_id,
  guest_type,
  sortBy = "",
  sortOrder = "",
  searchTerm = ""
) => {
  const params = new URLSearchParams({
    patient_id,
    guest_type,
    ...(sortBy && { sortBy }),
    ...(sortOrder && { sortOrder }),
    ...(searchTerm && { search: searchTerm }),
  });

  return api.get(`patients/guests/denied?${params.toString()}`);
};


/**
 * Adds a Denied Guest or Friend.
 *
 * @param {object} deniedGuestData - The data for the denied guest (e.g., name, reason, relation, etc.).
 * @returns {Promise<any>} A promise that resolves to the response after adding the denied guest.
 */
export const addDeniedGuest = async (deniedGuestData) => {
  const response = await api.post("appointments/guests/add", deniedGuestData);
  return response.data;
};

/**
 * Retrieves outpatient appointments for a specific patient with optional sorting.
 *
 * @param {string|number} patientId - The unique ID of the patient.
 * @param {string} [sortBy] - The field to sort by (e.g., "appointment_date").
 * @param {string} [sortOrder] - The sort order ("asc" or "desc").
 * @returns {Promise<any>} A promise that resolves to the outpatient appointments.
 */
export const getOutpatientAppointmentsByPatientId = async (patientId, sortBy, sortOrder) => {
  const response = await api.get(`appointments/patient/${patientId}`, {
    params: {
      ...(sortBy && { sortBy }),
      ...(sortOrder && { sortOrder }),
    },
  });
  return response.data;
};

/**
 * Retrieves future appointments for a specific patient with optional sorting.
 *
 * @param {string|number} patientId - The unique ID of the patient.
 * @param {string} [sortBy] - The field to sort by (e.g., "appointment_date").
 * @param {string} [sortOrder] - The sort order ("asc" or "desc").
 * @returns {Promise<any>} A promise that resolves to the future appointments.
 */
export const getFutureAppointmentsByPatientId = async (patientId, sortBy, sortOrder) => {
  const response = await api.get(`appointments/patient/${patientId}/future`, {
    params: {
      ...(sortBy && { sortBy }),
      ...(sortOrder && { sortOrder }),
    },
  });
  return response.data;
};

/**
 * Retrieves screening matches for a specific guest.
 *
 * @param {string|number} appointmentGuestScreeningId - The unique ID of the guest screening.
 * @returns {Promise<any>} A promise that resolves to the screening matches for the guest.
 */
export const getGuestScreeningMatches = async (appointment_guest_screening_id) => {
    const response = await api.get(
      `appointments/guests/${appointment_guest_screening_id}/screening/matches`
    );
    return response.data;
  };

  /**
 * Overrides the screening of a guest.
 *
 * @param {string|number} appointmentGuestScreeningId - The unique ID of the guest screening.
 * @param {object} overrideData - The data for overriding the screening (e.g., reason, status, etc.).
 * @returns {Promise<any>} A promise that resolves to the response after overriding the screening.
 */
export const overrideGuestScreening = async (appointmentGuestScreeningId, overrideData) => {
  const response = await api.patch(
    `appointments/guests/${appointmentGuestScreeningId}/screen/override`,
    overrideData
  );
  return response.data;
};
