import React, { useState } from 'react';
import ImageCapture from './ImageCapture';

const EditPhotoModal = ({ onClose, onSave }) => {
  const [imageSrc, setImageSrc] = useState(null);

  const handleSave = () => {
    if (imageSrc) {
      onSave(imageSrc);  
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-20">
      <div className="bg-white rounded-lg p-6 w-2/5 ">
        <div className=''>
          <ImageCapture
            handleSave={handleSave}
            onClose={onClose}
            onImageCaptured={(image) => setImageSrc(image)}
            onImageUploaded={(image) => setImageSrc(image)}
          />
        </div>
      </div>
    </div>
  );
};

export default EditPhotoModal;
