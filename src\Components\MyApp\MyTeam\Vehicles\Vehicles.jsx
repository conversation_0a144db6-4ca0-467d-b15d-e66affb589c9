import React, { useState, useMemo } from "react";
import GenericTable from "../../../GenericTable";
import AddVehicleModal from "./AddVehicleModal"; // Import the new modal component

const Vehicles = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [vehicleData, setVehicleData] = useState([
    {
      plate: "RJ 22 MI 2345",
      year: "2025",
      make: "2000",
      model: "Hero",
      color: "Black",
      updatedDate: "19-Mar-2025",
    },
    // ...other rows...
  ]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const vehicleColumns = [
    { name: "Plate", selector: (row) => row.plate, sortable: true },
    { name: "Year", selector: (row) => row.year },
    { name: "Make", selector: (row) => row.make },
    { name: "Model", selector: (row) => row.model },
    { name: "Color", selector: (row) => row.color },
    { name: "Updated Date", selector: (row) => row.updatedDate },
  ];

  const filteredVehicleData = useMemo(() => {
    if (!searchTerm) return vehicleData;
    return vehicleData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [vehicleData, searchTerm]);

  const handleAddVehicle = (newVehicle) => {
    setVehicleData((prevData) => [newVehicle, ...prevData]); // Add new vehicle at the top
    setIsModalOpen(false);
  };

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        onAdd={() => setIsModalOpen(true)} // Open modal on add button click
        title={"My Vehicles"}
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        columns={vehicleColumns}
        data={filteredVehicleData}
        fixedHeader
        fixedHeaderScrollHeight="400px"
        highlightOnHover
        striped
      />
      {isModalOpen && (
        <AddVehicleModal
          isOpen={isModalOpen} // Pass isOpen prop
          onClose={() => setIsModalOpen(false)}
          onSave={handleAddVehicle}
        />
      )}
    </div>
  );
};

export default Vehicles;
