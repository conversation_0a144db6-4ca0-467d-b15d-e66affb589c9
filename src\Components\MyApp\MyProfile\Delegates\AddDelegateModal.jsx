import React, { useState } from "react";
import CustomDropdown from "../../../Global/CustomDropdown";
import DateTimeInput from "../../../Temporary/DateTimeInput";
// import Input from "../../../Global/Input/Input";
import SearchableDropdown from "../../../Global/SearchableDropdownIdentity";
import { toast} from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const AddDelegateModal = ({ isOpen, onClose, onAdd }) => {
  const [show, setShow] = useState(false);
  const [formData, setFormData] = useState({
    searchIdentity: { name: "", eid: "" },
    taskType: "",
    sendNotificationTo: "",
    startDate: "",
    endDate: "",
    reason: [],               // ← state key
  });

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // const handleChange = (e) => {
  //   const { name, value } = e.target;
  //   setFormData({ ...formData, [name]: value });
  // };

  const handleSubmit = (e) => {
    e.preventDefault();
    onAdd(formData);
    toast.success("Delegation added successfully!"); 
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Delegation</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 rounded-lg">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">
            Delegation Details
          </h2>

          <div className="flex mb-4 items-center">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">Search Identity *</label>
            <div className="w-3/4">
              <SearchableDropdown
                value={formData.searchIdentity.name}
                onSelect={(selected) =>
                  setFormData({ ...formData, searchIdentity: selected })
                }
              />
            </div>
          </div>

          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Task To Delegate *
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={[
                  { label: "Approve Leave Requests", value: "approve_leave" },
                  { label: "Review Timesheets", value: "review_timesheets" },
                  { label: "Approve Expenses", value: "approve_expenses" },
                  { label: "Conduct Interviews", value: "conduct_interviews" },
                  { label: "Client Communication", value: "client_communication" },
                  { label: "Project Supervision", value: "project_supervision" },
                  { label: "Daily Reporting", value: "daily_reporting" },
                  { label: "Performance Reviews", value: "performance_reviews" },
                  { label: "Resource Allocation", value: "resource_allocation" },
                  { label: "Team Management", value: "team_management" },
                ]
                }
                placeholder="Select a task"
                value={formData.taskType}
                onSelect={(val) =>
                  setFormData({ ...formData, taskType: val })
                }
              />
            </div>
          </div>

          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Send Notification To
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={[
                  { label: "Manager", value: "manager" },
                  { label: "HR Department", value: "hr" },
                  { label: "Team Lead", value: "team_lead" },
                  { label: "Delegatee", value: "delegatee" },
                  { label: "Project Coordinator", value: "project_coordinator" },
                  { label: "Admin", value: "admin" },
                  { label: "IT Support", value: "it_support" },
                ]
                }
                placeholder="Select user"
                value={formData.sendNotificationTo}
                onSelect={(val) =>
                  setFormData({ ...formData, sendNotificationTo: val })
                }
              />
            </div>
          </div>

          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Start Date *
            </label>
            <div className="w-3/4">
              <DateTimeInput
                value={formData.startDate}
                onChange={(date) =>
                  setFormData({ ...formData, startDate: date })
                }
                placeholder="Select start date & time"
              />
            </div>
          </div>

          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              End Date *
            </label>
            <div className="w-3/4">
              <DateTimeInput
                value={formData.endDate}
                onChange={(date) =>
                  setFormData({ ...formData, endDate: date })
                }
                placeholder="Select end date & time"
              />
            </div>
          </div>
          <div className="flex mb-2 items-start">
  <label className="w-1/4 mt-2">Reason *</label>
  <div className="w-3/4">
    <div className="border rounded p-2 max-h-[120px] overflow-y-auto flex flex-wrap gap-2">
      {/* Selected bubbles on top */}
      {formData.reason &&
        formData.reason.map((selected, index) => (
          <div
            key={`selected-${index}`}
            className="bg-[#4F2683] text-white px-3 py-1 rounded-full text-sm flex items-center gap-1"
          >
            {selected}
            <button
              type="button"
              className="ml-1 text-xs font-bold"
              onClick={() => {
                const updated = formData.reason.filter((r) => r !== selected);
                setFormData({ ...formData, reason: updated });
              }}
            >
              ✕
            </button>
          </div>
        ))}

      {/* Bubble options (not already selected) */}
      {[
        "Vacation",
        "Sick Leave",
        "Business Trip",
        "Training",
        "Conference",
        "High Workload",
        "Personal Emergency",
        "System Maintenance",
        "Meeting Offsite",
      ]
        .filter((option) => !formData.reason.includes(option))
        .map((option) => (
          <button
            key={option}
            type="button"
            className="px-3 py-1 rounded-full border text-sm bg-gray-100 text-gray-700"
            onClick={() => {
              const updated = [...formData.reason, option];
              setFormData({ ...formData, reason: updated });
            }}
          >
            {option}
          </button>
        ))}
    </div>
  </div>
</div>

          <div className="flex justify-center gap-4 mt-6">
            <button
              type="button"
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded"
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#4F2683] text-white px-4 py-2 rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDelegateModal;
