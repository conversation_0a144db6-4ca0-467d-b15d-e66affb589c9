import React, { useState } from "react";
import Button from "../../../Global/Button";
import Input from "../../../Global/Input/Input";
import CustomDropdown from "../../../Global/CustomDropdown"
import DateInput from "../../../Global/Input/DateInput";

const AddCardForm = ({ onClose, onAdd }) => {
  const [show, setShow] = useState(false);
  const [activeTab, setActiveTab] = useState("Details");
  const [formData, setFormData] = useState({
    cardFormat: "",
    cardType: "",
    activationDate: "",
    deactivationDate: "",
    justification: "",
    shippingRequired: "No",
    facility: "",
    address: {
      shipTo: "",
      name: "",
      line1: "",
      line2: "",
      country: "India",
      state: "",
      city: "",
      postalCode: "",
      mobilePhone: "",
    },
  });

  const countryStateMap = {
    US: [
      { label: "California", value: "CA" },
      { label: "Texas", value: "TX" },
      { label: "New York", value: "NY" },
    ],
    CA: [
      { label: "Ontario", value: "ON" },
      { label: "British Columbia", value: "BC" },
      { label: "Quebec", value: "QC" },
    ],
    IN: [
      { label: "Maharashtra", value: "MH" },
      { label: "Karnataka", value: "KA" },
      { label: "Delhi", value: "DL" },
    ],
    AU: [
      { label: "New South Wales", value: "NSW" },
      { label: "Victoria", value: "VIC" },
      { label: "Queensland", value: "QLD" },
    ],
  };

  const [selectedCountry, setSelectedCountry] = useState(formData.address.country);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name === "shippingRequired" && value === "No") {
      // Clear facility when shipping is set to "No"
      setFormData({ ...formData, [name]: value, facility: "" });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleAddressChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      address: { ...formData.address, [name]: value },
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onAdd(formData);
  };

  // Optionally implement facility search logic if required.
  const handleFacilitySearch = () => {
    console.log("Facility search triggered");
  };

  // Show address fields when shipping is "No" OR shipping is "Yes" and facility has a value.
  const showAddressFields =
    formData.shippingRequired === "No" ||
    (formData.shippingRequired === "Yes" && formData.facility);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Card Request</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <div className="flex space-x-4 pt-4 ps-4">
          <button
            type="button"
            className={`w-1/4
               py-2 rounded-full 
              font-medium
              ${activeTab === "Details"
                ? "bg-[#4F2683] text-white"              // Filled (active)
                : "border border-[#4F2683] text-[#4F2683] bg-white" // Outlined (inactive)
              }
            `}
            onClick={() => setActiveTab("Details")}
          >
            Details
          </button>
          <button
            type="button"
            className={`w-1/4
             py-2 rounded-full 
              font-medium
              ${activeTab === "Card Shipment"
                ? "bg-[#4F2683] text-white"              // Filled (active)
                : "border border-[#4F2683] text-[#4F2683] bg-white" // Outlined (inactive)
              }
            `}
            onClick={() => setActiveTab("Card Shipment")}
          >
            Card Shipment
          </button>
        </div>
        <form onSubmit={handleSubmit} className="p-6 rounded-lg">
          {activeTab === "Details" && (
            <>
              <h2 className="text-[20px] text-[#333333] font-medium pb-4">
                Card Details
              </h2>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Card Format *
                </label>
                <div className="w-3/4">
                  <Input
                    type="text"
                    name="cardFormat"
                    placeholder="Card Format"
                    value={formData.cardFormat}
                    onChange={handleChange}
                  />
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Card Type
                </label>
                <div className="w-3/4">
                  <CustomDropdown
                    options={[
                      { label: "Credit Card", value: "credit" },
                      { label: "Debit Card", value: "debit" },
                      { label: "Prepaid Card", value: "prepaid" },
                    ]}
                    placeholder="Select Card Type"
                    value={formData.cardType}
                    onSelect={(value) => handleChange({ target: { name: "cardType", value } })}
                  />
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Activation Date *
                </label>
                <div className="w-3/4">
                  <DateInput
                    value={formData.activationDate}
                    onChange={(date) => handleChange({ target: { name: "activationDate", value: date } })}
                    placeholder="Select Activation Date"
                  />
                </div>
              </div>

              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Deactivation Date
                </label>
                <div className="w-3/4">
                  <DateInput
                    value={formData.deactivationDate}
                    onChange={(date) => handleChange({ target: { name: "deactivationDate", value: date } })}
                    placeholder="Select Deactivation Date"
                  />
                </div>
              </div>
              <div className="flex mb-2 items-center">
                <label className="w-1/4">Justification</label>
                <div className="w-3/4">
                  <Input
                    name="justification"
                    type="bubbles"
                    placeholder="Justification"
                    value={formData.justification}
                    height="94px"
                    bubbles={true}
                    bubbleOptions={[
                      "Lost Permanent Card",
                      "Forgot Permanent Card",
                    ]}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div className="flex justify-center gap-4 mt-6">
                <Button type="cancel" label="Cancel" onClick={onClose} />
                <Button
                  type="primary"
                  label="Next"
                  onClick={() => setActiveTab("Card Shipment")}
                />
              </div>
            </>
          )}
          {activeTab === "Card Shipment" && (
            <>
              <h2 className="text-[20px] text-[#333333] font-medium pb-4">
                Card Shipment
              </h2>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Shipping Required
                </label>
                <div className="w-3/4">
                  <CustomDropdown
                    options={[
                      { label: "No", value: "No" },
                      { label: "Yes", value: "Yes" },
                    ]}
                    value={formData.shippingRequired}
                    onSelect={(value) => handleChange({ target: { name: "shippingRequired", value } })}
                    placeholder="Select Shipping Option"
                    className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                  />
                </div>
              </div>
              {/* Facility Field */}
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Card Issuance / Pick up Facility
                </label>
                <div className="w-3/4">
                  <input
                    type="text"
                    name="facility"
                    placeholder="Enter Facility"
                    value={formData.facility}
                    onChange={handleChange}
                    className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                    disabled={formData.shippingRequired === "No"}
                  />
                </div>
              </div>
              {/* Address Fields */}
              {showAddressFields && (
                <>
                  <h2 className="text-[20px] text-[#333333] font-medium pb-4">
                    Address
                  </h2>
                  <div className="flex items-center mb-4">
                    <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                      Ship To *
                    </label>
                    <div className="w-3/4">
                      <input
                        type="text"
                        name="shipTo"
                        placeholder="Ship To"
                        value={formData.address.shipTo}
                        onChange={handleAddressChange}
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                      />
                    </div>
                  </div>
                  <div className="flex items-center mb-4">
                    <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                      Ship To Name
                    </label>
                    <div className="w-3/4">
                      <input
                        type="text"
                        name="name"
                        placeholder="Ship To Name"
                        value={formData.address.name}
                        onChange={handleAddressChange}
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                      />
                    </div>
                  </div>
                  <div className="flex items-center mb-4">
                    <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                      Address Line 1
                    </label>
                    <div className="w-3/4">
                      <input
                        type="text"
                        name="line1"
                        placeholder="Address Line 1"
                        value={formData.address.line1}
                        onChange={handleAddressChange}
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                      />
                    </div>
                  </div>
                  <div className="flex items-center mb-4">
                    <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                      Address Line 2
                    </label>
                    <div className="w-3/4">
                      <input
                        type="text"
                        name="line2"
                        placeholder="Address Line 2"
                        value={formData.address.line2}
                        onChange={handleAddressChange}
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                      />
                    </div>
                  </div>
                  <div className="flex items-center mb-4">
                    <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                      Country
                    </label>
                    <div className="w-3/4">
                      <CustomDropdown
                        options={[
                          { label: "United States", value: "US" },
                          { label: "Canada", value: "CA" },
                          { label: "India", value: "IN" },
                          { label: "Australia", value: "AU" },
                        ]}
                        value={selectedCountry}
                        onSelect={(value) => {
                          setSelectedCountry(value);
                          handleAddressChange({ target: { name: "country", value } });

                          // Reset state when country changes
                          handleAddressChange({ target: { name: "state", value: "" } });
                        }}
                        placeholder="Select Country"
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                      />
                    </div>
                  </div>

                  {/* State Dropdown */}
                  <div className="flex items-center mb-4">
                    <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                      State
                    </label>
                    <div className="w-3/4">
                      <CustomDropdown
                        options={countryStateMap[selectedCountry] || []}
                        value={formData.address.state}
                        onSelect={(value) => handleAddressChange({ target: { name: "state", value } })}
                        placeholder="Select State"
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                      />
                    </div>
                  </div>
                  <div className="flex items-center mb-4">
                    <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                      City
                    </label>
                    <div className="w-3/4">
                      <input
                        type="text"
                        name="city"
                        placeholder="City"
                        value={formData.address.city}
                        onChange={handleAddressChange}
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                      />
                    </div>
                  </div>
                  <div className="flex items-center mb-4">
                    <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                      Postal / Zip Code
                    </label>
                    <div className="w-3/4">
                      <input
                        type="text"
                        name="postalCode"
                        placeholder="Postal / Zip Code"
                        value={formData.address.postalCode}
                        onChange={handleAddressChange}
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                      />
                    </div>
                  </div>
                  <div className="flex items-center mb-4">
                    <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                      Mobile Phone *
                    </label>
                    <div className="w-3/4">
                      <input
                        type="text"
                        name="mobilePhone"
                        placeholder="Mobile Phone"
                        value={formData.address.mobilePhone}
                        onChange={handleAddressChange}
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                      />
                    </div>
                  </div>
                </>
              )}
              <div className="flex justify-center gap-4 mt-6">
                <Button
                  type="cancel"
                  label="Back"
                  onClick={() => setActiveTab("Details")}
                />
                <Button type="primary" label="Add" onClick={(e) => handleSubmit(e)} />
              </div>
            </>
          )}
        </form>
      </div>
    </div>
  );
};

export default AddCardForm;
