import { useState, useCallback, useEffect } from "react";
import { getRoomsByFloor } from "../api/global";
import { toast } from "react-toastify";

// Module-level cache for room data keyed by floorId
let roomMasterDataCache = {};

export const useRoomData = (floorId) => {
  const [rooms, setRooms] = useState(
    floorId && roomMasterDataCache[floorId]
      ? roomMasterDataCache[floorId]
      : []
  );

  const fetchRooms = useCallback(async () => {
    if (!floorId) {
      console.log("useRoomData: No floorId provided");
      return;
    }

    console.log("useRoomData: Fetching rooms for floorId:", floorId);
    try {
      const response = await getRoomsByFloor(floorId);
      console.log("useRoomData: API response:", response);
      const roomArray = response.data?.data || [];
      const fetchedRooms = roomArray.map((r) => ({
        label: r.room_number,
        value: r.room_id,
      }));
      console.log("useRoomData: Mapped rooms:", fetchedRooms);
      roomMasterDataCache[floorId] = fetchedRooms;
      setRooms(fetchedRooms);
    } catch (error) {
      console.error("useRoomData: Error fetching rooms:", error);
      toast.error("Error fetching rooms");
    }
  }, [floorId]);

  useEffect(() => {
    fetchRooms();
  }, [floorId, fetchRooms]);

  return rooms;
};
