import React, { useState, useEffect } from "react";
import DataTable from "react-data-table-component";
import Input from "./Global/Input/Input";
import Button from "../Components/Global/Button";

const defaultCustomStyles = {
  headCells: {
    style: {
      fontWeight: 400,
      fontSize: "15px",
      color: "#9971CB",
      textAlign: "left",
    },
  },
  cells: {
    style: {
      textAlign: "left",
    },
  },
};

export const FilterButtons = ({ filter,className,buttonClass,onFilterChange, filterOptions = [] }) => {
  return (
    <div className={`flex space-x-2 ${className}`}>
      {filterOptions.map((option) => (
        <Button
          key={option.value}
          type={filter === option.value ? "primary" : "outline"}
          onClick={() => onFilterChange(option.value)}
          label={option.label}
          className={`h-11 flex items-center justify-center ${buttonClass}`}
          rounded={true}
        />
      ))}
    </div>
  );
};

const GenericTableHeader = ({
  title,
  searchTerm,
  onSearchChange,
  onAdd,
  extraControls,
  showSearch,
  showAddButton = true,
  Checkboxes = false,
  checkboxFunction,
  passValueToSearch,
}) => {
  const [selectedCheckbox, setSelectedCheckbox] = useState("");

  const handleCheckboxChange = (type) => {
    if (selectedCheckbox === type) {
      setSelectedCheckbox("");
      checkboxFunction("");
    } else {
      setSelectedCheckbox(type);
      checkboxFunction(type);
    }
  };

  const shouldShowSearch =
    typeof showSearch !== "undefined"
      ? showSearch
      : (searchTerm !== undefined && searchTerm !== null) || onSearchChange;

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h3>{title}</h3>

        <div className="flex items-center space-x-2">
          {shouldShowSearch && (
            <div className="flex items-center space-x-2">
              <label htmlFor="table-search" className="text-lg font-normal text-gray-600">
                Search
              </label>
              <Input
                id="table-search"
                type="text"
                value={searchTerm}
                onChange={(e) => {
                  if (passValueToSearch) {
                    onSearchChange(e.target.value);
                  } else {
                    onSearchChange(e);
                  }
                }}
                placeholder="Search"

                className="h-[32px] p-2"
              />
            </div>
          )}
          {extraControls}
          {showAddButton && (
            <Button
              type="primary"
              className="py-0 flex items-center h-[32px]"
              rounded={true}
              onClick={onAdd}
              label="Add"
            />
          )}
        </div>
      </div>

      {/* ✅ Fix: Checkbox handlers properly passed */}
      {Checkboxes && checkboxFunction && (
        <div className="flex space-x-4 mt-2">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              className="form-checkbox"
              checked={selectedCheckbox === "retain"}
              onChange={() => handleCheckboxChange("retain")}
            />
            <span>Retain All</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              className="form-checkbox"
              checked={selectedCheckbox === "revoke"}
              onChange={() => handleCheckboxChange("revoke")}
            />
            <span>Revoke All</span>
          </label>
        </div>
      )}
    </>
  );
};

const GenericDataTable = ({
  columns,
  data,
  customStyles,
  fixedHeader = true,
  fixedHeaderScrollHeight = "300px",
  highlightOnHover = false,
  striped = false,
  noDataComponent,
  onSort,
}) => {
  return (
    <DataTable
      columns={columns}
      data={data}
      customStyles={customStyles}
      fixedHeader={fixedHeader}
      fixedHeaderScrollHeight={fixedHeaderScrollHeight}
      highlightOnHover={highlightOnHover}
      striped={striped}
      onSort={onSort}
      sortServer={true}
      noDataComponent={noDataComponent}
    />
  );
};

const GenericTable = ({
  title,
  searchTerm,
  onSearchChange,
  onAdd,
  extraControls,
  columns,
  data,
  customStyles = defaultCustomStyles,
  fixedHeader = true,
  fixedHeaderScrollHeight = "300px",
  highlightOnHover = true,
  striped = false,
  showSearch,
  passValueToSearch = false,
  showAddButton = true,
  Checkboxes = false,
  checkboxFunction,
  onSort,
  rowClassName,
}) => {
  const [hadDataInitially, setHadDataInitially] = useState(false);
  useEffect(() => {
    if (data && data.length > 0) {
      setHadDataInitially(true);
    }
  }, [data]);

  const filteredData = searchTerm
    ? data.filter((item) =>
      Object.keys(item).some((key) =>
        String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
      )
    )
    : data;

  const noDataMessage =
    hadDataInitially && searchTerm ? "No Records Found" : "No Records Found";

  return (
    <div className="shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] rounded-[10px] p-4">
      <GenericTableHeader
        title={title}
        searchTerm={searchTerm}
        onSearchChange={onSearchChange}
        onAdd={onAdd}
        onSort={onSort}
        extraControls={extraControls}
        showSearch={showSearch}
        showAddButton={showAddButton}
        Checkboxes={Checkboxes}
        checkboxFunction={checkboxFunction}
        passValueToSearch={passValueToSearch}
      />
      <GenericDataTable
        columns={columns}
        data={filteredData}
        customStyles={customStyles}
        onSort={onSort}
        fixedHeader={fixedHeader}
        fixedHeaderScrollHeight={fixedHeaderScrollHeight}
        highlightOnHover={highlightOnHover}
        striped={striped}
        noDataComponent={noDataMessage}
        rowClassName={rowClassName}
      />
    </div>
  );
};

export default GenericTable;
