import React, { useState } from 'react';
import Clock from 'react-clock';
import 'react-clock/dist/Clock.css';
import { format } from 'date-fns';

const TimeInput = ({ label, value, onChange }) => {
  const [showClock, setShowClock] = useState(false); // Control clock visibility
  const [selectedTime, setSelectedTime] = useState(
    value ? new Date(`1970-01-01T${value}:00`) : new Date()
  );

  const handleClockChange = (time) => {
    setSelectedTime(time);
    const formattedTime = format(time, 'HH:mm'); // Format time as "HH:mm"
    onChange(formattedTime); // Update Formik field value
    setShowClock(false); // Hide clock after selection
  };

  const handleInputClick = () => {
    setShowClock((prev) => !prev); // Toggle clock visibility
  };

  return (
    <div className="relative">
      <label className="block mb-1">{label}</label>
      <input
        type="text"
        readOnly
        value={format(selectedTime, 'HH:mm')} // Display formatted time
        onClick={handleInputClick}
        className="p-2 border rounded w-full focus:ring-2 focus:ring-[#4F2683] cursor-pointer"
      />

      {showClock && (
        <div className="absolute z-10 mt-2 bg-white shadow-md rounded">
          <Clock
            value={selectedTime}
            onChange={handleClockChange}
            renderNumbers={true}
          />
        </div>
      )}
    </div>
  );
};

export default TimeInput;
