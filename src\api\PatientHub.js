import api from "./";


/**
 * Retrieves appointment details for a specific patient.
 *
 * @param {object} params - Query parameters to filter patient details.
 * @returns {Promise<any>} A promise that resolves to the appointment details of the specific patient.
 */
export const getPatientDetails = async (params = {}) => {
    const response = await api.get("patients/details", { params });
    return response.data; // Directly return the nested data object
};

/**
 * Searches for patients based on query parameters.
 *
 * @param {object} params - Query parameters to filter patients.
 * @returns {Promise<any>} A promise that resolves to the search results of patients.
 */
export const searchPatients = async (params = {}) => {
  const response = await api.get("patients/search", { params });
  return response.data;
};

/**
 * Retrieves the latest appointment details for a specific patient.
 *
 * @param {object} params - Query parameters to filter patient information.
 * @returns {Promise<any>} A promise that resolves to the patient information.
 */
export const getPatientInformation = async (params = {}) => {
  const response = await api.get("patients/information", { params });
  // return just the patient info object
  return response.data.data;
};

/**
 * Updates patient details.
 *
 * @param {string} patientId - The ID of the patient.
 * @param {object} data - The data to update patient details.
 * @returns {Promise<any>} A promise that resolves to the updated patient details.
 */
export const updatePatientDetails = async (patientId,data) => {

    console.log("🚀 ~ updatePatientDetails ~ data:", data)
    const response = await api.patch(`patients/${patientId}/details`,data);
    return response.data.data;
};

/**
 * Updates admission details for a patient.
 *
 * @param {string} patientId - The ID of the patient.
 * @param {object} data - The data to update admission details.
 * @returns {Promise<any>} A promise that resolves to the updated admission details.
 */
export const updateAdmissionDetails = async (patientId, data) => {
    const response = await api.patch(`patients/${patientId}/admission`, data);
    return response.data.data;
};

/**
 * Updates facility details for a patient.
 *
 * @param {string} patientId - The ID of the patient.
 * @param {object} data - The data to update facility details.
 * @returns {Promise<any>} A promise that resolves to the updated facility details.
 */
export const updateFacilityDetails = async (patientId, data) => {
    const response = await api.patch(`patients/${patientId}/facility`, data);
    return response.data.data;
};

/**
 * Updates or creates a patient address.
 *
 * @param {string} patientId - The ID of the patient.
 * @param {object} data - The data to update or create the patient address.
 * @returns {Promise<any>} A promise that resolves to the updated or created address.
 */
export const updatePatientAddress = async (patientId, data) => {
    const response = await api.patch(`patients/${patientId}/address`, data);
    return response.data.data;
};
/**
 * Retrieves guests for a specific patient.
 *
 * @param {object} params - Query parameters to filter guest details.
 * @param {string} params.sortBy - The field to sort by (e.g., 'guest_arrival_time').
 * @param {string} params.sortOrder - The sort order ('ASC' or 'DESC').
 * @returns {Promise<any>} A promise that resolves to the guest list of the specific patient.
 */
export const getPatientGuests = async (params = {}) => {
    const response = await api.get("patients/guests/list", { params });
    return response.data.data.data;
};
/**
 * Adds a denied guest or friend for a patient.
 *
 * @param {object} data - The data to add a denied guest or friend.
 * @returns {Promise<any>} A promise that resolves to the added guest or friend details.
 */export const addPatientGuest = async (data, guest_type) => {
    const response = await api.post(`patients/guests/add`, data, {
        params: { guest_type }, // Pass guest_type as a query parameter
    });
    return response.data.data;
};

/**
 * Retrieves friends of a specific patient.
 *
 * @param {object} params - Query parameters to filter friends.
 * @returns {Promise<any>} A promise that resolves to the list of friends.
 */
export const getPatientFriends = async (params = {}) => {
    const response = await api.get("patients/guests/friends", { params });
    return response.data.data.data;
};

/**
 * Retrieves denied guests of a specific patient with sorting options.
 *
 * @param {object} params - Query parameters to filter denied guests.
 * @param {string} [sortBy] - The field to sort by.
 * @param {string} [sortOrder] - The sort order (e.g., "ASC" or "DESC").
 * @returns {Promise<any>} A promise that resolves to the list of denied guests.
 */
export const getDeniedGuests = async (params = {}, sortBy, sortOrder) => {
    const response = await api.get(
        `patients/guests/denied?${sortBy ? `sortBy=${sortBy}&` : ""}${sortOrder ? `sortOrder=${sortOrder}&` : ""}`, 
        { params }
    );
    return response.data.data.data;
};

/**
 * Deletes a guest by ID.
 *
 * @param {string} guestId - The ID of the guest to delete.
 * @returns {Promise<any>} A promise that resolves to the deletion result.
 */
export const deletePatientGuest = async (patient_guest_id) => {
    const response = await api.delete(`patients/guests/${patient_guest_id}`);
    return response.data.data;
};

/**
 * Updates a guest's details. 
 *
 * @param {string} guestId - The ID of the guest to update.
 * @param {object} data - The data to update the guest details.
 * @returns {Promise<any>} A promise that resolves to the updated guest details.
 */
export const updatePatientGuest = async (guestId, data) => {
    const response = await api.patch(`patients/guests/${guestId}`, data);
    return response.data.data;
};

/**
 * Updates a friend's details for a specific patient.
 *
 * @param {string} patientGuestId - The ID of the friend (PatientGuest) to update.
 * @param {object} data - The data to update the friend's details.
 * @returns {Promise<any>} A promise that resolves to the updated friend's details.
 */
export const updatePatientFriend = async (patientGuestId, data) => {
    const response = await api.put(`patients/guests/${patientGuestId}/friend`, data);
    return response.data;
};

/**
 * Retrieves the history of a specific patient.
 *
 * @param {object} params - Query parameters to filter patient history.
 * @returns {Promise<any>} A promise that resolves to the patient history data.
 */
export const getPatientHistory = async (params = {}) => {
    const response = await api.get("patients/history", { params });
    return response.data;
};

/**
 * Updates a specific patient-guest’s details.
 *
 * @param {string} patientGuestId – The ID of the patient-guest to update.
 * @param {object} data – The partial properties to update on that guest.
 * @returns {Promise<any>} A promise resolving to the updated guest object.
 */
export const updatePatientGuestDetails = async (patientGuestId, data) => {
  const response = await api.patch(
    `patients/guests/${patientGuestId}`,
    data
  );
  return response.data.data;
};

/**
 * Retrieves the history of all patients.
 *
 * @returns {Promise<any>} A promise that resolves to the history data of all patients.
 */
export const getAllPatientHistory = async () => {
    const response = await api.get("patients/history/allpatient");
    return response.data;
};

export const getHL7Messages = async ({ mrn, search, sortBy, sortOrder, page = 1, limit = 10 }) => {
  const params = { search, sortBy, sortOrder, page, limit };
  const response = await api.get(`patients/hl7-messages/${mrn}`, { params });
  return response.data;
};

/**
 * Updates the image for a patient.
 *
 * @param {string} patientId - The ID of the patient.
 * @param {object} data - The data containing the new image URL.
 * @returns {Promise<any>} A promise that resolves to the updated patient image details.
 */
export const updatePatientImage = async (patientId, data) => {
    const response = await api.patch(`patients/${patientId}/image`, data);
    return response.data;
};


/**
 * Creates a document for a given watchlist entry.
 *
 * @param {string} watchlistId - The ID of the watchlist.
 * @param {object} data - The document payload to attach.
 * @returns {Promise<any>} A promise that resolves to the created document.
 */
export const createWatchlistDocument = async (watchlistId, data) => {
  const response = await api.post(`watchlist/${watchlistId}/document`, data);
  return response.data.data;
};

/**
 * Retrieves the document for a given watchlist entry.
 *
 * @param {string} watchlistId - The ID of the watchlist.
 * @returns {Promise<any>} A promise that resolves to the fetched document.
 */
export const getWatchlistDocument = async (watchlistId) => {
  const response = await api.get(`watchlist/${watchlistId}/document`);
  return response.data?.data;
};
