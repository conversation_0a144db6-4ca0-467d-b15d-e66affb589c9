import React, { useState } from "react";

const ViewCardDetails = ({ card, onClose }) => {
  const [activeTab, setActiveTab] = useState("Details");

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-3xl bg-white rounded-lg shadow-lg h-full overflow-y-auto">
        <div className="flex items-center mb-2 px-4 pt-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Card Details
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <div className="flex space-x-4 pt-4 ps-4">
          <button
            type="button"
            className={`w-1/4 py-2 rounded-full font-medium ${
              activeTab === "Details"
                ? "bg-[#4F2683] text-white"
                : "border border-[#4F2683] text-[#4F2683] bg-white"
            }`}
            onClick={() => setActiveTab("Details")}
          >
            Details
          </button>
          <button
            type="button"
            className={`w-1/4 py-2 rounded-full font-medium ${
              activeTab === "Card Shipment"
                ? "bg-[#4F2683] text-white"
                : "border border-[#4F2683] text-[#4F2683] bg-white"
            }`}
            onClick={() => setActiveTab("Card Shipment")}
          >
            Card Shipment
          </button>
        </div>
        <div className="p-6 rounded-lg">
          {activeTab === "Details" && (
            <>
              <h2 className="text-[20px] text-[#333333] font-medium pb-4">
                Card Information
              </h2>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Card Format *
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.cardFormat || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Card Type
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.cardType || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Activation Date *
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.activationDate || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Deactivation Date
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.deactivationDate || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Justification *
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.justification || "N/A"}
                  </p>
                </div>
              </div>
            </>
          )}
          {activeTab === "Card Shipment" && (
            <>
              <h2 className="text-[20px] text-[#333333] font-medium pb-4">
                Card Shipment
              </h2>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Shipping Required
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.shippingRequired || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Card Issuance / Pick up Facility
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.facility || "N/A"}
                  </p>
                </div>
              </div>
              <h2 className="text-[20px] text-[#333333] font-medium pb-4">
                Address
              </h2>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Ship To *
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.shipTo || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Ship To Name
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.name || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Address Line 1
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.line1 || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Address Line 2
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.line2 || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Country
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.country || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  State
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.state || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  City
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.city || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Postal / Zip Code
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.postalCode || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Mobile Phone *
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.mobilePhone || "N/A"}
                  </p>
                </div>
              </div>
            </>
          )}
         
        </div>
      </div>
    </div>
  );
};

export default ViewCardDetails;
