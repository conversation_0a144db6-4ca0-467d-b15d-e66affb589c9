import React, { useState } from "react";

const ViewCardDetails = ({ card, onClose }) => {
  const [show, setShow] = useState(false);
  const [activeTab, setActiveTab] = useState("Details");

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Card Details</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <div className="flex space-x-4 pt-4 ps-4">
          <button
            type="button"
            className={`w-1/4 py-2 rounded-full font-medium ${
              activeTab === "Details"
                ? "bg-[#4F2683] text-white"
                : "border border-[#4F2683] text-[#4F2683] bg-white"
            }`}
            onClick={() => setActiveTab("Details")}
          >
            Details
          </button>
          <button
            type="button"
            className={`w-1/4 py-2 rounded-full font-medium ${
              activeTab === "Card Shipment"
                ? "bg-[#4F2683] text-white"
                : "border border-[#4F2683] text-[#4F2683] bg-white"
            }`}
            onClick={() => setActiveTab("Card Shipment")}
          >
            Card Shipment
          </button>
        </div>
        <div className="p-6 rounded-lg">
          {activeTab === "Details" && (
            <>
              <h2 className="text-[20px] text-[#333333] font-medium pb-4">
                Card Information
              </h2>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Card Format *
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.cardFormat || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Card Type
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.cardType || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Activation Date *
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.activationDate || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Deactivation Date
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.deactivationDate || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Justification *
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.justification || "N/A"}
                  </p>
                </div>
              </div>
            </>
          )}
          {activeTab === "Card Shipment" && (
            <>
              <h2 className="text-[20px] text-[#333333] font-medium pb-4">
                Card Shipment
              </h2>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Shipping Required
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.shippingRequired || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Card Issuance / Pick up Facility
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.facility || "N/A"}
                  </p>
                </div>
              </div>
              <h2 className="text-[20px] text-[#333333] font-medium pb-4">
                Address
              </h2>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Ship To *
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.shipTo || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Ship To Name
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.name || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Address Line 1
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.line1 || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Address Line 2
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.line2 || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Country
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.country || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  State
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.state || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  City
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.city || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Postal / Zip Code
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.postalCode || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center mb-4">
                <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                  Mobile Phone *
                </label>
                <div className="w-3/4">
                  <p className="p-2 border h-11 rounded bg-gray-100">
                    {card.address?.mobilePhone || "N/A"}
                  </p>
                </div>
              </div>
            </>
          )}
         
        </div>
      </div>
    </div>
  );
};

export default ViewCardDetails;
