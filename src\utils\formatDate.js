const formatDateTime = (dateString) => {
  if(dateString === null || dateString === undefined) return '-- --';
  const date = new Date(dateString);

  // For invalid dates
  if (isNaN(date)) return 'Pending';

  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  const month = months[date.getMonth()];
  const day = String(date.getDate()).padStart(2, '0');
  const year = date.getFullYear();

  // const hours = date.getHours() % 12 || 12;
  // const minutes = String(date.getMinutes()).padStart(2, '0');
  // const ampm = date.getHours() >= 12 ? 'PM' : 'AM';

  return `${month}-${day}-${year} `;
};

export default formatDateTime;
