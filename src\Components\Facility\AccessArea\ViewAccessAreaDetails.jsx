import React from "react";

const ViewAccessAreaDetails = ({ accessAreaData, onClose }) => {
  return (
    <div className=" w-full p-2">
      <div className="flex items-center mb-2 px-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">
          Access Area Details
        </h2>
        <button
          className="w-8 h-8 bg-[#4F2683] text-2xl text-white rounded-full"
          onClick={onClose}
          type="button"
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />
      <div className="mx-auto bg-white p-4  rounded-lg">
        <div className="mb-4 flex items-center">
         <div className="w-1/4"><span className="text-[16px] font-normal">Facility:</span></div>  <div className="w-3/4"><span className="text-[#8F8F8F]">{accessAreaData.facility}</span></div>
        </div>
        <div className="mb-4 flex items-center">
         <div className="w-1/4"><span className="text-[16px] font-normal">Building:</span></div>  <div className="w-3/4"><span className="text-[#8F8F8F]">{accessAreaData.building}</span> </div>
        </div>
        <div className="mb-4 flex items-center">
         <div className="w-1/4"> <span className="text-[16px] font-normal">Floor Number:</span></div>  <div className="w-3/4"><span className="text-[#8F8F8F]">{accessAreaData.floor}</span></div>
        </div>
        <div className="mb-4 flex items-center">
         <div className="w-1/4">   <span className="text-[16px] font-normal">Room No.:</span> </div>  <div className="w-3/4"><span className="text-[#8F8F8F]">{accessAreaData.roomNo}</span></div>
        </div>
        <div className="mb-4 flex items-center">
         <div className="w-1/4"><span className="text-[16px] font-normal">Access Area Name:</span> </div>  <div className="w-3/4"><span className="text-[#8F8F8F]">{accessAreaData.name}</span></div>
        </div>
       
      </div>
        <div className="flex px-4 justify-end">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 bg-gray-400 text-white rounded"
          >
            Close
          </button>
        </div>
    </div>
  );
};

export default ViewAccessAreaDetails;
