import { useState, useCallback, useEffect, useMemo } from "react";
import { getMasterData } from "../api/global";
import { toast } from "react-toastify";

// Module-level cache for access area master data
let accessAreaMasterDataCache = null;

export const useAccessAreaMasterData = () => {
  const [masterData, setMasterData] = useState(
    accessAreaMasterDataCache || { access_area_status: [], access_area_type: [] }
  );

  const fetchMasterData = useCallback(async () => {
    if (accessAreaMasterDataCache) return;
    try {
      const res = await getMasterData({
        groups: ["access_area_status", "access_area_type"],
      });
      accessAreaMasterDataCache = res.data;
      setMasterData(res.data);
    } catch (error) {
      toast.error("Error fetching access area master data");
    }
  }, []);

  useEffect(() => {
    if (!accessAreaMasterDataCache) {
      fetchMasterData();
    }
  }, [fetchMasterData]);

  const statusOptions = useMemo(() => {
    return masterData.access_area_status.map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.access_area_status]);

  const typeOptions = useMemo(() => {
    return masterData.access_area_type.map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.access_area_type]);

  return { masterData, statusOptions, typeOptions };
};
