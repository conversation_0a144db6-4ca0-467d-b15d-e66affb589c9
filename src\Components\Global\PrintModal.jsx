import React from 'react';
import barcode from '../../Images/Barcode.svg';
import Logo from '../../Images/Logo.png';

const PrintModal = ({ guest, onClose }) => {
  const handlePrint = () => {
    const printWindow = window.open('', '_blank');

    // Write the content for the print window
    printWindow.document.write(`
      <html>
        <head>
          <title>Guest Pass</title>
          <link rel="stylesheet" href="print.css"> <!-- Link to print styles -->
        </head>
        <body>
          <div class="modalPrintClass">
            <h2>Guest Pass</h2>
            <img src="${guest.image}" alt="${guest.guestName}" class="guest-img" />
            <p>Name: ${guest.guestName}</p>
            <img src="${barcode}" alt="barcode" class="barcode-img" />
            <p>Date: ${new Date().toLocaleString()}</p> <!-- Include time -->
            <h2 class="visitor-p">Visitor</h2> <!-- Add visitor header -->
            <div class="qr-code" id="qr-code"></div>
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();

    // Close modal immediately after triggering print
    onClose();

    // Trigger print
    printWindow.onload = () => {
      printWindow.print();
    };
  };

  return (
    <div className="fixed z-10 inset-0 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white p-6 rounded-lg shadow-lg justify-center ">
        <h2 className="text-xl font-bold mb-4">Print Guest Pass</h2>
        <div className='flex flex-col items-center mb-4'>
          <img src={Logo} alt="logo" className="w-20 mb-4" />
          <img src={guest.image} alt={guest.guestName} className="guest-img w-40 rounded-md " />
        </div>
        <p className="mb-2">Name: {guest.guestName}</p>
        <p className="mb-4">Date: {new Date().toLocaleString()}</p> {/* Include time */}
        <h2 className='visitor-p'>Visitor</h2> {/* Local display of visitor header */}
        <img src={barcode} alt="barcode" />
        <div className="flex justify-center mb-4">
        </div>
        <div className='flex justify-between'>
          <button
            onClick={onClose}
            className="bg-[#979797] text-white px-4 py-2 rounded"
          >
            Close
          </button>
          <button
            onClick={handlePrint}
            className="bg-[#4F2683] text-white px-4 py-2 rounded mr-2"
          >
            Print Pass
          </button>
        </div>
      </div>
    </div>
  );
};

export default PrintModal;
