import { useState, useCallback, useEffect, useMemo } from "react";
import { getMasterData } from "../api/global";
import { toast } from "react-toastify";

// Module-level cache for room master data
let roomMasterDataCache = null;

export const useRoomMasterData = () => {
  // Initialize masterData with empty arrays to avoid undefined errors
  const [masterData, setMasterData] = useState(
    roomMasterDataCache || { room_status: [], room_occupancy_type: [] }
  );

  const fetchMasterData = useCallback(async () => {
    if (roomMasterDataCache) return;
    try {
      const res = await getMasterData({
        groups: ["room_status", "room_occupancy_type"],
      });
      // If the response has a .data property, use it; otherwise, use the response itself
      const data = res.data ? res.data : res;
      roomMasterDataCache = data;
      setMasterData(data);
    } catch (error) {
      toast.error("Error fetching room master data");
    }
  }, []);

  useEffect(() => {
    if (!roomMasterDataCache) {
      fetchMasterData();
    }
  }, [fetchMasterData]);

  const statusOptions = useMemo(() => {
    return (masterData.room_status || []).map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.room_status]);

  const occupancyOptions = useMemo(() => {
    return (masterData.room_occupancy_type || []).map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.room_occupancy_type]);

  return { masterData, statusOptions, occupancyOptions };
};
