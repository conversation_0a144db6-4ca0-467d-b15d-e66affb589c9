import React, { useState, useMemo } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Button from "../../Global/Button";
import Input from "../../Global/Input/Input";
import DateInput from "../../Global/Input/DateInput";
import CustomDropdown from "../../Global/CustomDropdown";
import { toast } from "react-toastify";
import { createTraining } from "../../../api/identity";
import { useLocation } from "react-router-dom";

const trainingFields = [
  { label: "Name", type: "text", placeholder: "Name", name: "name" },
  { label: "Course Number", type: "text", placeholder: "Course Number", name: "course_number" },
  { label: "Category", type: "text", placeholder: "Category", name: "category" },
  {
    label: "Course Type",
    type: "dropdown",
    placeholder: "Select Course Type",
    name: "course_type",
    options: [
      { label: "Online", value: 1 },
      { label: "Offline", value: 2 },
      { label: "Hybrid", value: 3 },
    ],
  },
  {
    label: "Recurrence",
    type: "dropdown",
    placeholder: "Select Recurrence",
    name: "recurrence",
    options: [
      { label: "One Time", value: 1 },
      { label: "Monthly", value: 2 },
      { label: "Quarterly", value: 3 },
      { label: "Yearly", value: 4 },
    ],
  },
  { label: "Due Date", type: "date", placeholder: "MM-DD-YYYY", name: "due_date" },
  { label: "Date Completed", type: "date", placeholder: "MM-DD-YYYY", name: "date_completed" },
  { label: "Score", type: "number", placeholder: "Score", name: "score" },
  {
    label: "Status",
    type: "dropdown",
    placeholder: "Select Status",
    name: "status",
    options: [
      { label: "Pass", value: 1 },
      { label: "Fail", value: 2 },
      { label: "Pending", value: 3 },
    ],
  },
];

const AddTrainingForm = ({ onClose, addTraining }) => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id");
  const trainingSchema = useMemo(
    () =>
      yup.object().shape({
        name: yup.string().required("Name is required"),
        course_number: yup.string().required("Course Number is required"),
        category: yup.string().required("Category is required"),
        course_type: yup.number().required("Course Type is required"),
        recurrence: yup.number().required("Recurrence is required"),
        due_date: yup.date().required("Due Date is required"),
        date_completed: yup.date().nullable(),
        score: yup.number().nullable(),
        status: yup.number().required("Status is required"),
      }),
    []
  );

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(trainingSchema),
  });

  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const submitFormHandler = async (data) => {
    console.log("Form submitted with data:", data);
    console.log("Form errors:", errors);

    setLoading(true);
    try {
      const processedData = {
        name: data.name,
        course_number: data.course_number,
        category: data.category,
        course_type: parseInt(data.course_type), // Convert to number
        recurrence: parseInt(data.recurrence), // Convert to number
        due_date: data.due_date ? data.due_date.toISOString().split("T")[0] : null,
        date_completed: data.date_completed ? data.date_completed.toISOString().split("T")[0] : null,
        score: data.score ? parseInt(data.score) : null,
        status: parseInt(data.status), // Convert to number
        identity_id: identityId, // Keep as string
      };

      console.log("Processed data being sent to API:", processedData);
      const result = await createTraining(processedData);
      console.log("API response:", result);

      toast.success("Training added successfully!");

      // Call the parent callback to refresh data
      if (addTraining) {
        addTraining(result);
      }

      onClose();
    } catch (error) {
      console.error("Error adding training:", error);
      toast.error("Failed to add training. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
           <div
  className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
  style={{ willChange: "transform" }}
>

        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
  <h2 className="text-[24px] md:text-[30px] font-normal text-[#4F2683]">
    Add Training
  </h2>
  <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
</div>
<div className="p-6">
  <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">
    <form onSubmit={handleSubmit(submitFormHandler)} className="p-6">

          <h2 className="text-[20px] text-[#333333] font-medium pb-4">Training Details</h2>

          {trainingFields.map(({ label, type, name, placeholder, options }, idx) => (
            <div key={idx} className="flex items-center mb-4">
              <label className="w-1/4 text-[16px] font-normal text-[#333333]">{label}</label>
              <div className="w-3/4">
                {type === "date" ? (
                  /* Render DateInput using Controller */
                  <Controller
                    control={control}
                    name={name}
                    defaultValue={null}
                    render={({ field }) => (
                      <DateInput
                        value={field.value}
                        onChange={(date) => field.onChange(date)}
                        placeholder={placeholder}
                        error={errors[name]}
                        className="w-full"
                      />
                    )}
                  />
                ) : type === "dropdown" ? (
                  /* Render CustomDropdown using Controller */
                  <Controller
                    control={control}
                    name={name}
                    defaultValue=""
                    render={({ field }) => (
                      <CustomDropdown
                      className="h-10"
                        options={options}
                        value={field.value}
                        onSelect={field.onChange}
                        placeholder={placeholder}
                        error={errors[name]}
                      />
                    )}
                  />
                ) : (
                  /* Render standard Input */
                  <>
                    <Input
                      type={type}
                      name={name}
                      placeholder={placeholder}
                      error={errors[name]}
                      {...register(name)}
                    />
                    {errors[name] && <p className="text-red-500 text-sm mt-1">{errors[name].message}</p>}
                  </>
                )}
              </div>
            </div>
          ))}

          <div className="flex justify-center gap-4 mt-6">
            <Button type="cancel" label="Cancel" onClick={onClose} />
            <Button
              type="primary"
              label={loading ? "Saving..." : "Add"}
              disabled={loading}
              onClick={() => console.log("Add button clicked")}
            />
          </div>
        </form>
  </div>
</div>
      </div>
    </div>
  );
};

export default AddTrainingForm;
