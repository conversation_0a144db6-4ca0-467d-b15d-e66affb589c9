import React, { useState, useMemo } from 'react';
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import {
  HiOutlineHandThumbUp,
  HiOutlineHandThumbDown,
  HiHandThumbUp,
  HiHandThumbDown
} from "react-icons/hi2";
import { Task } from '../../api/static';
import TruncatedCell from '../../Components/Tooltip/TruncatedCell';
import TruncatedRow from '../../Components/Tooltip/TrucantedRow';

const filterOptions = [
  { label: 'All', value: 'All' },
  { label: 'Inactive 90 Days', value: 'Inactive90d' },
  { label: 'No Peer Access', value: 'NoPeerAccess' },
  { label: 'Active 30 Days', value: 'Active30d' },
];

const ValidationTask = () => {
  // master data
  const [tasks, setTasks] = useState(Task);
  // which tab is active
  const [activeFilter, setActiveFilter] = useState(filterOptions[0].value);
  // search string
  const [searchQuery, setSearchQuery] = useState('');

  // update recommendation on a single task
  const updateRecStatus = (identity, newStatus) => {
    setTasks(prev =>
      prev.map(t =>
        t.identity === identity ? { ...t, recommendation: newStatus } : t
      )
    );
  };

  // bulk set via checkboxes
  const handleCheckboxChange = type => {
    const newRec = type === 'retain' ? 'ok' : 'notOk';
    setTasks(prev => prev.map(t => ({ ...t, recommendation: newRec })));
  };

  // compute filteredData based on activeFilter + searchQuery
  const filteredData = useMemo(() => {
    const now = new Date();
    return tasks
      .filter(t => {
        if (activeFilter === 'Inactive90d') {
          const d = new Date(t.lastAccess);
          return (now - d) / (1000 * 60 * 60 * 24) > 90;
        }
        if (activeFilter === 'Active30d') {
          const d = new Date(t.lastAccess);
          return (now - d) / (1000 * 60 * 60 * 24) <= 30;
        }
        if (activeFilter === 'NoPeerAccess') {
          return t.peerAccess === false;
        }
        return true; // 'All'
      })
      .filter(t =>
        t.identity.toLowerCase().includes(searchQuery.trim().toLowerCase())
      );
  }, [tasks, activeFilter, searchQuery]);

  const columns = [
    {
      name: <TruncatedCell text="Item" />,
      selector: row => row.item,
      cell: row => <TruncatedRow text={row.item} />,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Identity" />,
      selector: row => row.identity,
      cell: row => <TruncatedRow text={row.identity} />,
    },
    {
      name: <TruncatedCell text="Type" />,
      selector: row => row.type,
      cell: row => <TruncatedRow text={row.type} />,
    },
    {
      name: <TruncatedCell text="Company" />,
      selector: row => row.company,
      cell: row => <TruncatedRow text={row.company} />,
    },
    {
      name: <TruncatedCell text="Organization" />,
      selector: row => row.organization,
      cell: row => <TruncatedRow text={row.organization} />,
    },
    {
      name: <TruncatedCell text="SA" />,
      selector: row => row.sa,
      cell: row => <TruncatedRow text={row.sa} />,
      center: true,
    },
    {
      name: <TruncatedCell text="Last Access" />,
      selector: row => row.lastAccess,
      cell: row => <TruncatedRow text={row.lastAccess} />,
      center: true,
    },
    {
      name: <TruncatedCell text="Recommendation" />,
      cell: row => (
        <div className="flex justify-center">
          {row.recommendation === 'ok' ? (
            <div className="p-1 rounded-full bg-opacity-8 bg-[#4F268314]">
              <HiHandThumbUp className="text-[#4F2683] text-xl" />
            </div>
          ) : row.recommendation === 'notOk' ? (
            <div className="p-1 rounded-full bg-opacity-17 bg-[#8F8F8F2B]">
              <HiHandThumbDown className="text-[#8F8F8F] text-xl" />
            </div>
          ) : (
            <HiOutlineHandThumbUp className="text-[#8F8F8F] text-xl" />
          )}
        </div>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      center: true,
      width: '120px',
    },
    {
      name: "Action",
      cell: row => (
        <div className="flex gap-2">
          <button
            className="flex gap-1 p-1 whitespace-nowrap w-full border text-[#4F2683] rounded-full"
            onClick={() => updateRecStatus(row.identity, "ok")}
          >
            <HiOutlineHandThumbUp className="text-xl" />
            <p>Retain</p>
          </button>
          <button
            className="flex gap-1 p-1 whitespace-nowrap w-full border text-[#8F8F8F] rounded-full"
            onClick={() => updateRecStatus(row.identity, "notOk")}
          >
            <HiOutlineHandThumbDown className="text-xl" />
            <p>Revoke</p>
          </button>
        </div>
      ),
      center: true,
    },
    {
      name: "Status",
      selector: row => row.status,
      cell: row => (
        <span
          className={`w-24 p-1 flex justify-center items-center rounded-full ${
            row.status.toLowerCase() === "complete"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
      center: true,
      width: "150px",
    },
    {
      name: <TruncatedCell text="Comments" />,
      selector: row => row.comment,
      center: true,
    },
  ];

  return (
    <div className="ml-4 mt-1">
      {/* Tabs */}
      <div className="mb-4">
        <FilterButtons
          filter={activeFilter}
          onFilterChange={setActiveFilter}
          filterOptions={filterOptions}
        />
      </div>

      {/* Table */}
      <div className="bg-white rounded-[10px]">
        <GenericTable
          fixedHeaderScrollHeight="320px"
          title="Validation Tasks"
          searchTerm={searchQuery}
          onSearchChange={e => setSearchQuery(e.target.value)}
          columns={columns}
          data={filteredData}
          showSearch
          showAddButton={false}
          Checkboxes
          checkboxFunction={handleCheckboxChange}
        />
      </div>
    </div>
  );
};

export default ValidationTask;
