import React, { useState } from "react";
import Button from "../Global/Button";
import CustomDropdown from "../Global/CustomDropdown";

function ApproversEdit({ onSubmit, onClose, availableApprovers, initialData }) {
  // Determine if we are in edit mode (when initialData is provided)
  const isEditMode = Boolean(initialData);

  // For add mode, use availableApprovers to filter names;
  // for edit mode, we already have the name from initialData.
  const defaultApprovers = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
  ];

  const approversData =
    availableApprovers && availableApprovers.length
      ? availableApprovers
      : defaultApprovers;

  const approversNames =
    Array.isArray(approversData) && typeof approversData[0] === "object"
      ? approversData.map((item) => item.name)
      : approversData;

  // For edit mode, pre-set the selected approver and level.
  const [searchTerm, setSearchTerm] = useState(isEditMode ? initialData.name : "");
  const [selectedApprover, setSelectedApprover] = useState(isEditMode ? initialData.name : "");
  const [selectedLevel, setSelectedLevel] = useState(
    isEditMode ? initialData.approverLevel : "Level 1"
  );
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const filteredApprovers = approversNames.filter((name) =>
    name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectApprover = (name) => {
    setSelectedApprover(name);
    setSearchTerm(name);
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // In both modes, we need a selected approver.
    if (!selectedApprover) {
      alert("Please select an approver.");
      return;
    }
    let approverObj;
    if (
      Array.isArray(approversData) &&
      typeof approversData[0] === "object"
    ) {
      approverObj = approversData.find(
        (item) => item.name === selectedApprover
      );
      // Override the approverLevel with the selected level from our dropdown
      approverObj = {
        ...approverObj,
        approverLevel: selectedLevel,
      };
    } else {
      approverObj = { name: selectedApprover, approverLevel: selectedLevel };
    }
    onSubmit(approverObj);
    setShow(false);
    setTimeout(onClose, 700);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-white w-full max-w-3xl rounded-lg shadow-lg h-full p-0 transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        {/* Header */}
        <div className="flex items-center mb-2 px-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            {isEditMode ? "Edit Approver" : "Add Approver"}
          </h2>
          <button
            className="w-8 h-8 bg-[#4F2683] flex justify-center items-center p-0 text-white text-2xl rounded-full"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
            type="button"
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-white p-6 pt-2 rounded-lg my-3">
          {/* Approver Name Field */}
          <div className="mb-4 flex items-center">
            <label className="text-[16px] font-normal w-1/4">Approver Name</label>
            <div className="w-3/4">
              {isEditMode ? (
                // In edit mode, display the name as a read-only input.
                <input
                  type="text"
                  value={initialData.name}
                  disabled
                  className="w-full h-11 border border-gray-300 rounded px-3 bg-gray-100"
                />
              ) : (
                // In add mode, allow search & selection.
                <div className="relative w-full">
                  <input
                    type="text"
                    placeholder="Search Approver"
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      setIsDropdownVisible(true);
                    }}
                    onFocus={() => setIsDropdownVisible(true)}
                    onBlur={() =>
                      setTimeout(() => setIsDropdownVisible(false), 150)
                    }
                    className="w-full h-11 border border-gray-300 rounded px-3"
                  />
                  {isDropdownVisible && (
                    <div className="absolute top-full left-0 w-full mt-1 border bg-white rounded-md shadow-lg max-h-60 overflow-y-auto z-50">
                      {filteredApprovers.length > 0 ? (
                        filteredApprovers.map((name) => (
                          <div
                            key={name}
                            className="p-2 cursor-pointer hover:bg-gray-100"
                            onMouseDown={() => handleSelectApprover(name)}
                          >
                            {name}
                          </div>
                        ))
                      ) : (
                        <div className="p-2 text-gray-700 text-center">
                          No Results Found.
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Approver Level Dropdown */}
          <div className="mb-4 flex items-center">
            <label className="text-[16px] font-normal w-1/4">
              Approver Level
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={["Level 1", "Level 2", "Level 3"]}
                defaultValue="Level 1"
                value={selectedLevel}
                onSelect={(option) => setSelectedLevel(option)}
                placeholder="Select Level"
                bgColor="bg-white"
                textColor="text-black"
                // hoverBgColor="hover:bg-gray-200"
                borderColor="border-gray-300"
                rounded="rounded"
                className="w-full h-11"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-center">
            <Button
              type="button"
              label="Cancel"
              onClick={onClose}
              className="bg-gray-400 text-white"
            />
            <Button
              type="submit"
              label={isEditMode ? "Update" : "Add"}
              className="bg-[#4F2683] text-white"
            />
          </div>
        </form>
      </div>
    </div>
  );
}

export default ApproversEdit;
