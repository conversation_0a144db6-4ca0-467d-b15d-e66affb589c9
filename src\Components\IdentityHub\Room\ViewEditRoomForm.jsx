import React, { useState } from "react";
import Button from "../../Global/Button";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";
import { FaFileAlt } from "react-icons/fa";
import Attachment from "../../../Images/Attachment.png"; // Import the attachment image

const MySwal = withReactContent(Swal);

const ViewEditDocumentForm = ({ documentData, onUpdate, onClose }) => {
  const initialData = {
    documentType: documentData.documentName || "",
    otherDocument: documentData.documentName || "",
    documentNumber: documentData.documentNumber || "",
    status: documentData.status || "",
    issueDate: documentData.issueDate || "",
    expirationDate: documentData.expirationDate || "",
    country: documentData.country || "",
    state: documentData.state || "",
    otherIssuer: documentData.otherIssuer || "",
    note: documentData.note || "",
    attachment: documentData.attachment || "",
    uploadedDate: documentData.uploadedDate || "",
  };

  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState(initialData);

  const inputClassName = isEditMode
    ? "w-full border border-gray-300 rounded p-2"
    : "w-full border-none text-[#8F8F8F]";

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData((prev) => ({
        ...prev,
        attachment: URL.createObjectURL(file),
      }));
    }
  };

  const handleSave = (e) => {
    e.preventDefault();
    onUpdate({ ...documentData, ...formData });
    setIsEditMode(false);
  };

  const handleViewAttachment = (e) => {
    e.preventDefault();
    if (!formData.attachment) return;
    MySwal.fire({
      title: `<div class='text-[#4F2683] text-2xl font-normal'>Attachment Preview</div>`,
      showCloseButton: true,
      width: "60%",
      heightAuto: false,
      scrollable: true,
      html: `
        <div style="max-height: 500px; overflow-y: auto; padding: 10px;">
          <object data="${formData.attachment}" type="application/pdf" class="w-full h-[500px] border mt-4">
            <div style="text-align: center; padding: 20px;">
              <p>Your browser does not support inline viewing of this file.</p>
              <a 
                href="${formData.attachment}" 
                download="attachment" 
                style="display:inline-block; margin-top:10px; padding:10px 20px; background-color:#4F2683; color:#fff; border-radius:5px; text-decoration:none;"
              >
                Download File
              </a>
            </div>
          </object>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: "OK",
    });
  };

  return (
    <div className="w-full p-0">
      {/* Header */}
      <div className="flex items-center justify-between mb-2 px-2">
        <h2 className="text-[30px] font-normal text-[#4F2683]">
          Document Details
        </h2>
        <button
          className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          type="button"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />

      {/* Form */}
      <form onSubmit={handleSave} className="bg-white p-6 rounded-lg">
        <h2 className="text-[20px] text-[#333333] font-medium pb-4">
          Document Information
        </h2>

        {/* Document Type */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal text-[#333333]">
            Document Type*
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <CustomDropdown
                className="h-11 w-full border border-gray-300 rounded p-2"
                options={["Passport", "Driver License", "Other"]}
                onSelect={(option) =>
                  setFormData({ ...formData, documentType: option })
                }
                selectedOption={formData.documentType}
                placeholder="Select Document Type"
              />
            ) : (
              <Input
                type="text"
                value={formData.documentType}
                disabled
                className={inputClassName}
              />
            )}
          </div>
        </div>

        {/* Specify Document (if Other) */}
        {formData.documentType === "Other" && (
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Specify Document*
            </label>
            <div className="w-3/4">
              {isEditMode ? (
                <Input
                  type="text"
                  name="otherDocument"
                  value={formData.otherDocument}
                  onChange={handleChange}
                  className={inputClassName}
                />
              ) : (
                <Input
                  type="text"
                  value={formData.otherDocument}
                  disabled
                  className={inputClassName}
                />
              )}
            </div>
          </div>
        )}

        {/* Document Number */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal text-[#333333]">
            Document Number*
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <Input
                type="text"
                name="documentNumber"
                value={formData.documentNumber}
                onChange={handleChange}
                className={inputClassName}
              />
            ) : (
              <Input
                type="text"
                value={formData.documentNumber}
                disabled
                className={inputClassName}
              />
            )}
          </div>
        </div>

        {/* Status */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal text-[#333333]">
            Status
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <CustomDropdown
                className="h-11 w-full border border-gray-300 rounded p-2"
                options={["Active", "Inactive", "Expired"]}
                onSelect={(option) =>
                  setFormData({ ...formData, status: option })
                }
                selectedOption={formData.status}
                placeholder="Set Status"
              />
            ) : (
              <Input
                type="text"
                value={formData.status}
                disabled
                className={inputClassName}
              />
            )}
          </div>
        </div>

        {/* Issue Date */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal text-[#333333]">
            Issue Date*
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <Input
                type="date"
                name="issueDate"
                value={formData.issueDate}
                onChange={handleChange}
                className={inputClassName}
              />
            ) : (
              <Input
                type="text"
                value={formData.issueDate}
                disabled
                className={inputClassName}
              />
            )}
          </div>
        </div>

        {/* Expiration Date */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal text-[#333333]">
            Expiration Date*
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <Input
                type="date"
                name="expirationDate"
                value={formData.expirationDate}
                onChange={handleChange}
                className={inputClassName}
              />
            ) : (
              <Input
                type="text"
                value={formData.expirationDate}
                disabled
                className={inputClassName}
              />
            )}
          </div>
        </div>

        {/* Country */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal text-[#333333]">
            Country
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <Input
                type="text"
                name="country"
                value={formData.country}
                onChange={handleChange}
                className={inputClassName}
              />
            ) : (
              <Input
                type="text"
                value={formData.country}
                disabled
                className={inputClassName}
              />
            )}
          </div>
        </div>

        {/* State */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal text-[#333333]">
            State
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <Input
                type="text"
                name="state"
                value={formData.state}
                onChange={handleChange}
                className={inputClassName}
              />
            ) : (
              <Input
                type="text"
                value={formData.state}
                disabled
                className={inputClassName}
              />
            )}
          </div>
        </div>

        {/* Other Issuer */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal text-[#333333]">
            Other Issuer
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <Input
                type="text"
                name="otherIssuer"
                value={formData.otherIssuer}
                onChange={handleChange}
                className={inputClassName}
              />
            ) : (
              <Input
                type="text"
                value={formData.otherIssuer}
                disabled
                className={inputClassName}
              />
            )}
          </div>
        </div>

        {/* Note */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal text-[#333333]">
            Note
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <textarea
                name="note"
                value={formData.note}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
              ></textarea>
            ) : (
              <textarea
                value={formData.note}
                disabled
                className="w-full border-none text-[#8F8F8F]"
              />
            )}
          </div>
        </div>

        {/* Attachment */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal text-[#333333]">
            Attachment
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <div className="col">
                <label
                  style={{
                    display: "inline-block",
                    cursor: "pointer",
                    padding: "0.5rem",
                    backgroundColor: "#E5E7EB",
                    borderRadius: "0.375rem",
                  }}
                >
                  <img src={Attachment} alt="Attachment" />
                  <input
                    type="file"
                    onChange={handleFileChange}
                    style={{ display: "none" }}
                  />
                </label>
                {formData.attachment && (
                  <p className="mt-2 text-gray-700">
                    {/* Optionally, show the file name or a message */}
                    {/* File selected */}
                  </p>
                )}
              </div>
            ) : (
              formData.attachment && (
                <a
                  href="#"
                  onClick={handleViewAttachment}
                  className="flex items-center text-blue-500 underline cursor-pointer"
                >
                  <FaFileAlt className="mr-2" />
                  <span>View Attachment</span>
                </a>
              )
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-end mt-8">
          {!isEditMode ? (
            <button
              type="button"
              onClick={() => setIsEditMode(true)}
              className="px-6 py-2 bg-[#4F2683] text-white rounded hover:bg-[#3c1d64] transition-colors"
            >
              Edit
            </button>
          ) : (
            <>
              <Button
                type="button"
                onClick={() => {
                  setIsEditMode(false);
                  setFormData(initialData);
                }}
                label="Cancel"
                className="px-6 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 transition-colors"
              />
              <Button
                type="submit"
                label="Save"
                className="px-6 py-2 bg-[#4F2683] text-white rounded hover:bg-[#3c1d64] transition-colors"
              />
            </>
          )}
          <Button
            type="button"
            onClick={onClose}
            label="Close"
            className="px-6 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 transition-colors"
          />
        </div>
      </form>
    </div>
  );
};

export default ViewEditDocumentForm;
