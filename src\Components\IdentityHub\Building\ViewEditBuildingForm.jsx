import React, { useState, useEffect } from "react";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";

const ViewEditBuildingForm = ({ buildingData, onUpdate, onClose }) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({
    id: buildingData.id || "",
    facility: buildingData.facility || "",
    building: buildingData.building || "",
    buildingCode: buildingData.buildingCode || "",
    address: buildingData.address || "",
    city: buildingData.city || "",
    status: buildingData.status || "Active",
    type: buildingData.type || "",
    occupancyType: buildingData.occupancyType || "",
    buildingPhone: buildingData.buildingPhone || "",
    facilityEmail: buildingData.facilityEmail || "",
    geoLocationCode: buildingData.geoLocationCode || "",
    otherCode: buildingData.otherCode || "",
    buildingUrl: buildingData.buildingUrl || "",
    connectedApplication: buildingData.connectedApplication || "",
    facilityNotes: buildingData.facilityNotes || "",
  });

  useEffect(() => {
    setFormData({
      id: buildingData.id || "",
      facility: buildingData.facility || "",
      building: buildingData.building || "",
      buildingCode: buildingData.buildingCode || "",
      address: buildingData.address || "",
      city: buildingData.city || "",
      status: buildingData.status || "Active",
      type: buildingData.type || "",
      occupancyType: buildingData.occupancyType || "",
      buildingPhone: buildingData.buildingPhone || "",
      facilityEmail: buildingData.facilityEmail || "",
      geoLocationCode: buildingData.geoLocationCode || "",
      otherCode: buildingData.otherCode || "",
      buildingUrl: buildingData.buildingUrl || "",
      connectedApplication: buildingData.connectedApplication || "",
      facilityNotes: buildingData.facilityNotes || "",
    });
  }, [buildingData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = (e) => {
    e.preventDefault();
    onUpdate(formData);
    setIsEditMode(false);
  };

  // This className is used for read-only fields
  const inputClassName = `w-full border bg-transparent rounded p-2 ${
    isEditMode ? " focus:outline-none" : "border-none text-[#8F8F8F]"
  }`;
  // console.log("Building Data:", buildingData);
  return (
    <div className=" w-full p-2">
      <div className="flex items-center mb-2 px-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">
          Building Details
        </h2>
        <button
          className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          type="button"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />
      <form onSubmit={handleSave} className="bg-white p-6 rounded-lg">
        {/* Facility */}
        <div className="flex items-center mb-4">
          <label htmlFor="facility" className="w-1/4 text-[16px] font-normal">
            Facility
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <CustomDropdown
                className="h-11 rounded border-gray-300"
                options={["Facility 1", "Facility 2", "Facility 3"]}
                onSelect={(option) =>
                  setFormData({ ...formData, facility: option })
                }
                selectedOption={formData.facility}
                value={formData.facility}
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
              />
            ) : (
              <Input
                type="text"
                name="facility"
                id="facility"
                value={formData.facility}
                disabled
                className={inputClassName}
                label=""
              />
            )}
          </div>
        </div>

        {/* Building */}
        <div className="flex items-center mb-4">
          <label htmlFor="building" className="w-1/4 text-[16px] font-normal">
            Building
          </label>
          <div className="w-3/4">
          {isEditMode ? (
              <CustomDropdown
                className="h-11 rounded border-gray-300"
                options={["Building A", "Building B", "Building C"]}
                onSelect={(option) =>
                  setFormData({ ...formData, building: option })
                }
                selectedOption={formData.building}
                value={formData.building}
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
              />
            ) : (
              <Input
                type="text"
                name="building"
                id="building"
                value={formData.building}
                disabled
                className={inputClassName}
                label=""
              />
            )}
            {/* <Input
              type="text"
              name="building"
              id="building"
              value={formData.building}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
              label=""
            /> */}
          </div>
        </div>

        {/* Building Code */}
        <div className="flex items-center mb-4">
          <label htmlFor="buildingCode" className="w-1/4 text-[16px] font-normal">
            Building Code
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="buildingCode"
              id="buildingCode"
              value={formData.buildingCode}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
              label=""
            />
          </div>
        </div>

        {/* Address */}
        <div className="flex items-center mb-4">
          <label htmlFor="address" className="w-1/4 text-[16px] font-normal">
            Address
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="address"
              id="address"
              value={formData.address}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
              label=""
            />
          </div>
        </div>

        {/* City */}
        <div className="flex items-center mb-4">
          <label htmlFor="city" className="w-1/4 text-[16px] font-normal">
            City
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="city"
              id="city"
              value={formData.city}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
              label=""
            />
          </div>
        </div>

        {/* Status */}
        <div className="flex items-center mb-4">
          <label htmlFor="status" className="w-1/4 text-[16px] font-normal">
            Status
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <CustomDropdown
                className="h-11 rounded border-gray-300"
                options={["Active", "Expired"]}
                onSelect={(option) =>
                  setFormData({ ...formData, status: option })
                }
                selectedOption={formData.status}
                value={formData.status}
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
              />
            ) : (
              <Input
                type="text"
                name="status"
                id="status"
                value={formData.status}
                disabled
                className={inputClassName}
                label=""
              />
            )}
          </div>
        </div>

        {/* Type */}
        <div className="flex items-center mb-4">
          <label htmlFor="type" className="w-1/4 text-[16px] font-normal">
            Type
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <CustomDropdown
                className="h-11 rounded border-gray-300"
                options={["Type 1", "Type 2"]}
                onSelect={(option) =>
                  setFormData({ ...formData, type: option })
                }
                selectedOption={formData.type}
                value={formData.type}
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
              />
            ) : (
              <Input
                type="text"
                name="type"
                id="type"
                value={formData.type}
                disabled
                className={inputClassName}
                label=""
              />
            )}
          </div>
        </div>

        {/* Occupancy Type */}
        <div className="flex items-center mb-4">
          <label htmlFor="occupancyType" className="w-1/4 text-[16px] font-normal">
            Occupancy Type
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <CustomDropdown
                className="h-11 rounded border-gray-300"
                options={["OccupancyType 1", "OccupancyType 2"]}
                onSelect={(option) =>
                  setFormData({ ...formData, occupancyType: option })
                }
                selectedOption={formData.occupancyType}
                value={formData.occupancyType}
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
              />
            ) : (
              <Input
                type="text"
                name="occupancyType"
                id="occupancyType"
                value={formData.occupancyType}
                disabled
                className={inputClassName}
                label=""
              />
            )}
          </div>
        </div>

        {/* Building Phone */}
        <div className="flex items-center mb-4">
          <label htmlFor="buildingPhone" className="w-1/4 text-[16px] font-normal">
            Building Phone
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="buildingPhone"
              id="buildingPhone"
              value={formData.buildingPhone}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
              label=""
            />
          </div>
        </div>

        {/* Facility Email */}
        <div className="flex items-center mb-4">
          <label htmlFor="facilityEmail" className="w-1/4 text-[16px] font-normal">
            Facility Email
          </label>
          <div className="w-3/4">
            <Input
              type="email"
              name="facilityEmail"
              id="facilityEmail"
              value={formData.facilityEmail}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
              label=""
            />
          </div>
        </div>

        {/* Geo Location Code */}
        <div className="flex items-center mb-4">
          <label htmlFor="geoLocationCode" className="w-1/4 text-[16px] font-normal">
            Geo Location Code
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="geoLocationCode"
              id="geoLocationCode"
              value={formData.geoLocationCode}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
              label=""
            />
          </div>
        </div>

        {/* Other Code */}
        <div className="flex items-center mb-4">
          <label htmlFor="otherCode" className="w-1/4 text-[16px] font-normal">
            Other Code
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="otherCode"
              id="otherCode"
              value={formData.otherCode}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
              label=""
            />
          </div>
        </div>

        {/* Building URL */}
        <div className="flex items-center mb-4">
          <label htmlFor="buildingUrl" className="w-1/4 text-[16px] font-normal">
            Building URL
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="buildingUrl"
              id="buildingUrl"
              value={formData.buildingUrl}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
              label=""
            />
          </div>
        </div>

        {/* Connected Application */}
        <div className="flex items-center mb-4">
          <label htmlFor="connectedApplication" className="w-1/4 text-[16px] font-normal">
            Connected Application
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="connectedApplication"
              id="connectedApplication"
              value={formData.connectedApplication}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
              label=""
            />
          </div>
        </div>

        {/* Facility Notes */}
        <div className="flex items-center mb-4">
          <label htmlFor="facilityNotes" className="w-1/4 text-[16px] font-normal">
            Facility Notes
          </label>
          <div className="w-3/4">
          {isEditMode ? (
            <Input
              type="textarea"
              name="facilityNotes"
              id="facilityNotes"
              value={formData.facilityNotes}
              onChange={handleChange}
              className={inputClassName}
            />
          ) : (
            <Input
              type="text"
              name="facilityNotes"
              id="facilityNotes"
              value={formData.facilityNotes}
              disabled
              className={inputClassName}
              label=""
            />
          )}
          </div>
        </div>

        {/* Toggle between Edit and Save/Cancel */}
        <div className="flex gap-4 justify-end">
          {!isEditMode ? (
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                setIsEditMode(true);
              }}
              className="px-4 py-2 bg-[#4F2683] text-white rounded"
            >
              Edit
            </button>
          ) : (
            <>
              <button
                type="button"
                onClick={() => setIsEditMode(false)}
                className="px-4 py-2 bg-[#979797] text-white rounded"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-[#4F2683] text-white rounded"
              >
                Save
              </button>
            </>
          )}
        </div>
      </form>
    </div>
  );
};

export default ViewEditBuildingForm;
