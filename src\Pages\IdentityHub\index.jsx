import { useState, useEffect, useCallback } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import FilterPanel from "../../Components/Observation/FilterPanel";
import {
  cardList,
  getIdentity,
  getIdentityAccessDetails,
} from "../../api/identity"; // Import the API function
import { toast } from "react-toastify";
import { useCardData } from "../../hooks/useCardData";
import { useIdentityData } from "../../hooks/useIdentityData";
import newWindow from "../../Images/new-window.svg";
const IdentityHub = () => {
  const { statusOptions, templateMap, statusMap } = useCardData();
  const {
    identityStatusOptions,
    accessStatusOptions,
    identityTypeMap,
    identityStatusMap,
  } = useIdentityData();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false); // Loading state
  const [activeTab, setActiveTab] = useState("Identity");
  const [filter, setFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [cardData, setCardData] = useState([]);
  const [identityData, setIdentityData] = useState([]);
    const [hoveredRow, setHoveredRow] = useState(null);

  const [identityAccessData, setIdentityAccessData] = useState([]);
  const [sortParams, setSortParams] = useState({
    Identity: { sortBy: "", sortOrder: "ASC" },
    Cards: { sortBy: "", sortOrder: "ASC" },
    Access: { sortBy: "", sortOrder: "ASC" },
  });

  const handleSortClick = (column, sortDirection) => {
    console.log("IdentityHub - Sort clicked:", { column, sortDirection, activeTab });
    // Update sortParams for the current tab
    setSortParams((prev) => {
      const newSortParams = {
        ...prev,
        [activeTab]: {
          sortBy: column.id,
          sortOrder: sortDirection.toUpperCase(),
        },
      };
      console.log("IdentityHub - New sortParams:", newSortParams);
      return newSortParams;
    });
  };

  useEffect(() => {
    fetchIdentityData();
    fetchCardData();
    fetchIdentityAccessData();
  }, []);

  useEffect(() => {
    if (location.state && location.state.newIdentity) {
      navigate(location.pathname, { replace: true });
    }
  }, [location, navigate]);

  const identityColumns = [
    {
      id: "first_name",
      name: "Name",
      selector: (row) => `${row.first_name} ${row.last_name}`, // Combine first and last name
      sortable: true,
      cell: (row, index) => (
        <div
          className="relative flex items-center space-x-3 cursor-pointer group w-full"
          onMouseEnter={() => setHoveredRow(index)}
          onMouseLeave={() => setHoveredRow(null)}
          onClick={() => {
            console.log("Row data:", row);
            console.log("Available keys:", Object.keys(row));
            const identityId = row.identity_id || row.id || row.Identity_id;
            console.log("Identity ID:", identityId);
            navigate(`/identity-details?identity_id=${identityId}`);
          }}
        >
          <span>{`${row.first_name} ${row.last_name}`}</span>
          {hoveredRow === index && (
            <img className="w-4 h-4" src={newWindow} alt="Open in new window" />
          )}
        </div>
      ),
    },
    {
      id: "eid",
      name: "EID",
      selector: (row) => row.eid, // Match the API field for EID
      sortable: true,
    },
    {
      id: "identity_type",
      name: "Type",
      selector: (row) => identityTypeMap[row.identity_type] || "Unknown",
      sortable: true,
    },
    {
      id: "email",
      name: "Email",
      selector: (row) => row.email || "N/A",
      sortable: true,
    },
    {
      id: "company",
      name: "Company",
      selector: (row) => row.company || "N/A", // Handle missing data
      sortable: true,
    },
    {
      id: "organization",
      name: "Organization",
      selector: (row) => row.organization || "N/A", // Handle missing data
      sortable: true,
    },
    {
      id: "job_title",
      name: "Job Title",
      selector: (row) => row.job_title || "N/A", // Handle missing data
      sortable: true,
    },
    {
      id: "end_date",
      name: "End Date",
      selector: (row) =>
        row.end_date
          ? new Date(row.end_date).toLocaleDateString() // Format date
          : "N/A",
      sortable: true,
    },
    {
      id: "status",
      name: "Status",
      selector: (row) => identityStatusMap[row.status] || "Unknown",
      sortable: true,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full text-sm font-medium ${
            (identityStatusMap[row.status] || "Unknown").toLowerCase() === "active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {identityStatusMap[row.status] || "Unknown"}
        </span>
      ),
    },
  ];

  // Columns for Cards and Access remain unchanged
  const cardsColumns = [
    {
      id: "card_number",
      name: "Card Number",
      selector: (row) => row.card_number,
      sortable: true,
    },
    {
      id: "template",
      name: "Card Type",
      selector: (row) => templateMap[row.template] || "Unknown",
      sortable: true,
    },
    {
      id: "created_by",
      name: "Owner (EID)",
      selector: (row) => row.created_by,
      sortable: true,
    },
    {
      id: "deactive_date",
      name: "Expiry",
      selector: (row) => row.deactive_date,
      sortable: true,
    },
    {
      id: "status",
      name: "Status",
      selector: (row) => statusMap[row.status] || "Unknown",
      sortable: true,
    },
  ];

  const accessColumns = [
    {
      id: "access_level_name",
      name: "Area Name",
      selector: (row) => row.access_level_name,
      sortable: true,
    },
    {
      id: "card_number",
      name: "Card Number",
      selector: (row) => row.card_number,
      sortable: true,
    },
    {
      id: "start_date",
      name: "Start Date",
      selector: (row) =>
        row.start_date ? new Date(row.start_date).toLocaleDateString() : "N/A",
      sortable: true,
    },
    {
      id: "end_date",
      name: "End Date",
      selector: (row) =>
        row.end_date ? new Date(row.end_date).toLocaleDateString() : "N/A",
      sortable: true,
    },
    {
      id: "created_by",
      name: "Created By",
      selector: (row) => row.created_by,
      sortable: true,
    },
    {
      id: "status",
      name: "Status",
      selector: (row) =>
        row.status === 0
          ? "Assignment"
          : row.status === 1
          ? "Revoked"
          : "Unknown",
      sortable: true,
    },
  ];

  // Helper to get numeric status value for API
  const getNumericStatus = (filterValue, options) => {
    if (filterValue === "All") return undefined;
    // If filterValue is already a number, return as is
    if (typeof filterValue === "number") return filterValue;
    // Try to find the option with matching label or value
    const found = options.find(
      (opt) => opt.label === filterValue || opt.value === filterValue
    );
    return found ? Number(found.value) : undefined;
  };

  // Prepare filter options dynamically from master data
  const identityFilterOptions = [
    { label: "All", value: "All" },
    ...identityStatusOptions.map((opt) => ({
      label: opt.label,
      value: opt.value,
    })),
  ];
  const cardFilterOptions = [
    { label: "All", value: "All" },
    ...statusOptions.map((opt) => ({ label: opt.label, value: opt.value })),
  ];
  const accessFilterOptions = [
    { label: "All", value: "All" },
    ...accessStatusOptions.map((opt) => ({
      label: opt.label,
      value: opt.value,
    })),
  ];

  let dataForTable = [];
  let columnsForTable = [];
  let filterOptions = [];

  if (activeTab === "Identity") {
    dataForTable = Array.isArray(identityData) ? identityData : []; // Ensure dataForTable is an array
    columnsForTable = identityColumns;
    filterOptions = identityFilterOptions;
  } else if (activeTab === "Cards") {
    dataForTable = Array.isArray(cardData) ? cardData : []; // Ensure dataForTable is an array
    columnsForTable = cardsColumns;
    filterOptions = cardFilterOptions;
  } else if (activeTab === "Access") {
    dataForTable = Array.isArray(identityAccessData) ? identityAccessData : [];
    columnsForTable = accessColumns;
    filterOptions = accessFilterOptions;
  }

  const identityCount = Array.isArray(identityData) ? identityData.length : 0;
  const cardsCount = Array.isArray(cardData) ? cardData.length : 0;
  const accessCount = Array.isArray(identityAccessData)
    ? identityAccessData.length
    : 0;

  const fetchIdentityData = useCallback(async () => {
    setLoading(true);
    try {
      // Clear previous data while loading new results
      setIdentityData([]);
      const statusNumeric = getNumericStatus(filter, identityStatusOptions);
      const params = {
        status: statusNumeric,
        search: searchTerm || undefined,
        sortBy: sortParams.Identity.sortBy || undefined,
        sortOrder: sortParams.Identity.sortOrder || undefined,
      };

      console.log("IdentityHub - fetchIdentityData params:", params);
      const res = await getIdentity(params);
      console.log("IdentityHub - fetchIdentityData response:", res);
      setIdentityData(res.data.data);
    } catch (error) {
      console.error("IdentityHub - fetchIdentityData error:", error);
      toast.error("Error fetching identities: " + error.message);
    } finally {
      setLoading(false);
    }
  }, [filter, searchTerm, sortParams.Identity, identityStatusOptions]);

  const fetchCardData = useCallback(async () => {
    setLoading(true);
    try {
      setCardData([]);
      const statusNumeric = getNumericStatus(filter, statusOptions);
      const params = {
        status: statusNumeric,
        search: searchTerm || undefined,
        sortBy: sortParams.Cards.sortBy || undefined,
        sortOrder: sortParams.Cards.sortOrder || undefined,
      };
      console.log("IdentityHub - fetchCardData params:", params);
      const res = await cardList(params);
      console.log("IdentityHub - fetchCardData response:", res);
      let cards = res.data;
      setCardData(cards);
    } catch (error) {
      console.error("IdentityHub - fetchCardData error:", error);
      toast.error("Error fetching card data: " + error.message);
    } finally {
      setLoading(false);
    }
  }, [filter, searchTerm, sortParams.Cards, statusOptions]);

  const fetchIdentityAccessData = useCallback(async () => {
    setLoading(true);
    try {
      setIdentityAccessData([]);
      const statusNumeric = getNumericStatus(filter, accessStatusOptions);
      const params = {
        status: statusNumeric,
        search: searchTerm || undefined,
        sortBy: sortParams.Access.sortBy || undefined,
        sortOrder: sortParams.Access.sortOrder || undefined,
      };
      console.log("IdentityHub - fetchIdentityAccessData params:", params);
      const res = await getIdentityAccessDetails(params);
      console.log("IdentityHub - fetchIdentityAccessData response:", res);
      // res is already the data array since getIdentityAccessDetails returns response.data.data
      setIdentityAccessData(res);
    } catch (error) {
      console.error("IdentityHub - fetchIdentityAccessData error:", error);
      toast.error("Error fetching access data: " + error.message);
    } finally {
      setLoading(false);
    }
  }, [filter, searchTerm, sortParams.Access, accessStatusOptions]);

  useEffect(() => {
    const fetchData = async () => {
      if (activeTab === "Identity") {
        await fetchIdentityData();
      } else if (activeTab === "Cards") {
        await fetchCardData();
      } else if (activeTab === "Access") {
        await fetchIdentityAccessData();
      }
    };
    fetchData();
    // Add fetchIdentityData, fetchCardData, fetchIdentityAccessData to dependencies
  }, [
    filter,
    searchTerm,
    activeTab,
    fetchIdentityData,
    fetchCardData,
    fetchIdentityAccessData,
    sortParams,
  ]);

  return (
    <div className="flex flex-col px-8 py-4 pl-20 pt-20">
      <h2 className="font-normal text-2xl text-[#4F2683] mb-4">Identity Hub</h2>

      {/* Tabs */}
      <div className="flex items-center space-x-6 mb-6">
        <button
          className={`p-2 flex text-[15px] font-normal items-center justify-center ${
            activeTab === "Identity"
              ? "text-[#fff] rounded-md bg-[#4F2683]"
              : "text-gray-500"
          }`}
          onClick={() => {
            setActiveTab("Identity");
            setFilter("All");
            setSearchTerm("");
          }}
        >
          Identity ({identityCount})
        </button>
        <button
          className={`p-2 flex text-[15px] font-normal items-center justify-center ${
            activeTab === "Cards"
              ? "text-[#fff] rounded-md bg-[#4F2683]"
              : "text-gray-500"
          }`}
          onClick={() => {
            setActiveTab("Cards");
            setFilter("All"); 
            setSearchTerm("");
          }}
        >
          Cards ({cardsCount})
        </button>
        <button
          className={`p-2 flex text-[15px] font-normal items-center justify-center ${
            activeTab === "Access"
              ? "text-[#fff] rounded-md bg-[#4F2683]"
              : "text-gray-500"
          }`}
          onClick={() => {
            setActiveTab("Access");
            setFilter("All");
            setSearchTerm("");
          }}
        >
          Access ({accessCount})
        </button>
      </div>

      <div className="mb-4 flex items-center space-x-4">
        <FilterButtons
          filter={filter}
          onFilterChange={setFilter}
          filterOptions={filterOptions}
        />
      </div>
      <GenericTable
        title="Identity Hub"
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        onSort={handleSortClick}
        columns={columnsForTable}
        onAdd={() => navigate("/add-Identity")}
        data={dataForTable} // Use filtered and sorted data
        fixedHeader
        fixedHeaderScrollHeight="440px"
      />

      <FilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
      />
    </div>
  );
};

export default IdentityHub;
