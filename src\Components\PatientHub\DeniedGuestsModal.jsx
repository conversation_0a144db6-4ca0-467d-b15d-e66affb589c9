import React, { useState, useEffect } from "react";
import Input from "../Global/Input/Input";
import { updatePatientGuestDetails } from "../../api/PatientHub";
import { toast } from "react-toastify";
import DateInput from "../Global/Input/DateInput";
function DeniedGuestsModal({ guestData, onUpdate, onClose }) {
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({
    id: guestData.id || "",
    first_name: guestData.first_name || "",
    last_name: guestData.last_name || "",
    denialReason: guestData.denialReason || "",
    contactEmail: guestData.contactEmail || "",
    contactNumber: guestData.contactNumber || "",
    dateDenied: guestData.dateDenied || ""
  });
  const [show, setShow] = useState(false);

  useEffect(() => {
    setFormData({
      id: guestData.id || "",
      first_name: guestData.first_name || "",
      last_name: guestData.last_name || "",
      denialReason: guestData.denialReason || "",
      contactEmail: guestData.contactEmail || "",
      contactNumber: guestData.contactNumber || "",
      dateDenied: guestData.dateDenied || ""
    });
  }, [guestData]);

  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const inputClassName = `w-full border bg-transparent rounded p-2 ${isEditMode ? "focus:outline-none" : "border-none text-[#8F8F8F]"
    }`;

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = async (e) => {
    e.preventDefault();
    try {
      // Call patch API
      const updated = await updatePatientGuestDetails(formData.id, {
        first_name: formData.first_name,
        last_name: formData.last_name,
        reason: formData.denialReason,
        email: formData.contactEmail,
        phone: formData.contactNumber,
        denied_on: formData.dateDenied // if backend expects ISO, convert here
      });

      // Map response to table row shape
      const updatedRow = {
        id: updated.patient_guest_id || formData.id,
        guestName: `${updated.first_name || ""} ${updated.last_name || ""}`.trim(),
        denialReason: updated.reason,
        contactEmail: updated.email,
        contactNumber: updated.phone,
        dateDenied: updated.denied_on
          ? new Date(updated.denied_on).toLocaleString()
          : formData.dateDenied
      };

      onUpdate(updatedRow);
      setIsEditMode(false);
      toast.success("Denied guest details updated successfully!");
    } catch (err) {
      console.error("Error updating denied guest:", err);
      toast.error(
        err?.response?.data?.message ||
        "Failed to update. Please try again."
      );
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-3xl bg-white rounded-lg shadow-lg h-full p-0 transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center mb-2 px-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Denied Guest Details
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form onSubmit={handleSave} className="bg-white p-6 rounded-lg">
          <div className="flex items-center mb-4">
            <label htmlFor="guestName" className="w-1/4 text-[16px] font-normal">
              first Name
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="first_name"
                id="first_name"
                value={formData.first_name}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
                placeholder="First Name"
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="guestName" className="w-1/4 text-[16px] font-normal">
              Last Name
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="last_name"
                id="last_name"
                value={formData.last_name}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
                placeholder="Last Name"
              />
            </div>
          </div>

          <div className="flex items-center mb-4">
            <label htmlFor="contactEmail" className="w-1/4 text-[16px] font-normal">
              Email
            </label>
            <div className="w-3/4">
              <Input
                type="email"
                name="contactEmail"
                id="contactEmail"
                value={formData.contactEmail}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="contactNumber" className="w-1/4 text-[16px] font-normal">
              Number
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="contactNumber"
                id="contactNumber"
                value={formData.contactNumber}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="dateDenied" className="w-1/4 text-[16px] font-normal">
              Date of Birth
            </label>
            <div className="w-3/4">
              {isEditMode ? (
                <DateInput
                  
                  name="dateDenied"
                  id="dateDenied"
                  value={formData.dateDenied}
                  onChange={(date) =>
                    setFormData(prev => ({ ...prev, dateDenied: date }))
                  }
                  disabled={!isEditMode}
                  className="w-full rounded-sm"
                  placeholder="YYYY-MM-DD"
                />
              ) : (
                <div className="p-2 text-[#8F8F8F]">
                  {formData.dateDenied || "N/A"}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="denialReason" className="w-1/4 text-[16px] font-normal">
              Denial Reason
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="denialReason"
                id="denialReason"
                value={formData.denialReason}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          <div className="flex gap-4 justify-end">
            {!isEditMode ? (
              <button
                type="button"
                onClick={() => setIsEditMode(true)}
                className="px-4 py-2 bg-[#4F2683] text-white rounded"
              >
                Edit
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={() => {
                    setIsEditMode(false);
                    setFormData({
                      id: guestData.id || "",
                      guestName: guestData.guestName || "",
                      denialReason: guestData.denialReason || "",
                      contactEmail: guestData.contactEmail || "",
                      contactNumber: guestData.contactNumber || "",
                      dateDenied: guestData.dateDenied || ""
                    });
                  }}
                  className="px-4 py-2 bg-gray-400 text-white rounded"
                >
                  Cancel
                </button>
                <button type="submit" className="px-4 py-2 bg-[#4F2683] text-white rounded">
                  Save
                </button>
              </>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

export default DeniedGuestsModal;
