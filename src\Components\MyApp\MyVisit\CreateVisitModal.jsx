import React from "react";
import { useForm } from "react-hook-form";
import moment from "moment";

const CreateVisitModal = ({ visible, onClose, onSave }) => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      title: "",
      type: "",
      category: "",
      host: "",
      escort: "",
      startDate: moment().format("YYYY-MM-DDTHH:mm"), // default to now
      endDate: moment().add(1, "hour").format("YYYY-MM-DDTHH:mm"),
      status: "Approved", // or any default
    },
  });

  const onSubmit = (data) => {
    // Optionally transform date/time format to match table display
    const formattedData = {
      ...data,
      startDate: moment(data.startDate).format("DD-MMM-YYYY | hh:mm A"),
      endDate: moment(data.endDate).format("DD-MMM-YYYY | hh:mm A"),
    };

    onSave(formattedData);
    reset();      // Reset form
    onClose();    // Close the modal
  };

  if (!visible) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white w-full max-w-lg rounded shadow-lg p-6 relative">
        {/* Close Button */}
        <button
          className="absolute top-2 right-2 text-xl bg-[#4F2683] text-white rounded-full w-8 h-8 flex items-center justify-center"
          onClick={onClose}
        >
          &times;
        </button>

        <h2 className="text-2xl mb-4 text-[#4F2683]">Create Event</h2>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Visit Title */}
          <div>
            <label className="block mb-1 text-sm font-medium">Event Title</label>
            <input
              type="text"
              {...register("title", { required: "Title is required" })}
              className="border border-gray-300 rounded w-full px-3 py-2"
              placeholder="Enter visit title"
            />
            {errors.title && (
              <p className="text-red-500 text-xs mt-1">{errors.title.message}</p>
            )}
          </div>

          {/* Type */}
          <div>
            <label className="block mb-1 text-sm font-medium">Type</label>
            <input
              type="text"
              {...register("type")}
              className="border border-gray-300 rounded w-full px-3 py-2"
              placeholder="e.g., Professional"
            />
          </div>

          {/* Category */}
          <div>
            <label className="block mb-1 text-sm font-medium">Category</label>
            <input
              type="text"
              {...register("category")}
              className="border border-gray-300 rounded w-full px-3 py-2"
              placeholder="e.g., Scheduled"
            />
          </div>

          {/* Host */}
          <div>
            <label className="block mb-1 text-sm font-medium">Host</label>
            <input
              type="text"
              {...register("host")}
              className="border border-gray-300 rounded w-full px-3 py-2"
              placeholder="e.g., John Doe"
            />
          </div>

          {/* Escort */}
          <div>
            <label className="block mb-1 text-sm font-medium">Escort</label>
            <input
              type="text"
              {...register("escort")}
              className="border border-gray-300 rounded w-full px-3 py-2"
              placeholder="e.g., Shawn Terry"
            />
          </div>

          {/* Start Date & Time */}
          <div>
            <label className="block mb-1 text-sm font-medium">Start Date & Time</label>
            <input
              type="datetime-local"
              {...register("startDate", { required: "Start date is required" })}
              className="border border-gray-300 rounded w-full px-3 py-2"
            />
            {errors.startDate && (
              <p className="text-red-500 text-xs mt-1">{errors.startDate.message}</p>
            )}
          </div>

          {/* End Date & Time */}
          <div>
            <label className="block mb-1 text-sm font-medium">End Date & Time</label>
            <input
              type="datetime-local"
              {...register("endDate")}
              className="border border-gray-300 rounded w-full px-3 py-2"
            />
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-4 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="bg-gray-400 text-white px-4 py-2 rounded"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#4F2683] text-white px-4 py-2 rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateVisitModal;
