{"layout": {"sidebar": "Sidebar", "header": "Header"}, "access_area": {"title": "Access Area", "area_name": "Area Name", "pacs_area_name": "PACS Area Name", "facility": "Facility", "system": "System", "online": "Online", "requestable_in_self_service": "Requestable in Self Service", "area_types": "Area Type(s)", "card_types": "Card Type(s)", "status": "Status", "status_active": "Active", "status_deleted": "Deleted", "filter_all": "All", "filter_active": "Active", "filter_deleted": "Deleted"}, "access_area_details": {"title": "Access Area", "area_name": "Area Name", "creation_date": "Creation Date", "area_type": "Area Type", "requestable_in_self_service": "Requestable In Self Service", "tab_area_details": "Area Details", "tab_facility": "Facility(s)", "tab_owners_approvers": "Owners & Approvers", "tab_perimeter_areas": "Perimeter Areas", "tab_assigned_identities": "Assigned Identities"}, "access_area_group": {"title": "Access Group", "area_group_name": "Area Group Name", "type": "Type", "creation": "Creation", "requestable_in_self_service": "Requestable in Self Service", "status": "Status", "status_active": "Active", "status_deleted": "Deleted", "filter_all": "All", "filter_active": "Active", "filter_deleted": "Deleted"}, "access_area_group_details": {"title": "Access Group", "area_name": "Area Name", "creation_date": "Creation date", "requestable_in_self_service": "Requestable in Self Service", "status": "Status", "tab_group_details": "Group Details", "tab_areas": "Areas", "tab_owners_approvers": "Owners & Approvers", "tab_assigned_identities": "Assigned Identities"}, "chat_modal": {"title": "Cha<PERSON>", "eid": "EID", "name": "Name", "enter_message": "Enter your message:", "cancel": "Cancel", "send": "Send"}, "credential_details": {"title": "Credential Hub", "name": "ADAM L'THELAN", "eid": "EID", "created_on": "Created On", "status": "Status", "status_unprinted_badges": "Unprinted Badges", "tab_view_request": "View Request", "tab_task": "Task"}, "print_modal": {"title": "Print Credential Badge", "front": "Front", "back": "Back", "default_name": "<PERSON>", "visitor": "Visitor", "expire": "Expire", "info1": "It is a long established fact that a reader will be distracted by the readable content of a page.", "info2": "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.", "print": "Print", "cancel": "Cancel"}, "credential_hub": {"title": "Credential Hub", "table_title": "Credentials", "name": "Name", "eid": "EID", "type": "Type", "badge_template": "Badge Template", "created_on": "Created On", "badging_facility": "Badging Facility", "shipment_address": "Shipment Address", "status": "Status", "status_unprinted": "Unprinted", "status_printed": "Printed", "status_in_queue": "Badges in Queue", "status_completed": "Completed", "status_pending_photo_upload": "Pending Photo Upload", "status_completed_photo_upload": "Completed Photo Upload", "status_print_failed_badges": "Print Failed Badges", "action": "Action"}, "facility": {"title": "Facility", "name": "Facility", "address": "Address", "state_province": "State/Province", "country": "Country", "status": "Status", "status_active": "Active", "status_inactive": "Inactive", "type": "Type", "open_in_new_window": "Open in new window", "filter_all": "All", "filter_active": "Active", "tab_facility": "Facility", "tab_building": "Building", "tab_floor": "Floor", "tab_room": "Room", "tab_access_areas": "Access Areas"}, "doctors_appointment": {"search_patient_placeholder": "Search by Patient Name, MRN", "search_guest_placeholder": "Search for guests", "not_available": "N/A", "name": "Name", "guest_pin": "Guest Pin", "screening": "Screening", "screening_alert": "Screening <PERSON><PERSON>", "appointment_time": "Appointment Time", "arrival_time": "Arrival Time", "location": "Location", "action": "Action", "add_guest": "Add Guest", "update_profile_image": "Update Profile Image", "capture_picture": "Capture Picture", "chat": "Cha<PERSON>", "nda": "NDA", "print": "Print", "print_guest_label": "Print Guest Label", "check_in": "Check In", "guest_check_in": "Guest Check-In", "check_out": "Check Out", "guest_check_out": "Guest Check-Out", "patient_name": "Patient Name", "doctors_name": "Doctor's Name", "in": "In", "out": "Out", "camera": "Camera", "no_results_found": "No Results Found", "todays_appointments": "Today's Appointments", "all_future_appointments": "All Future Appointments"}, "inpatient_visit": {"search_patient_placeholder": "Search by Patient Name, MRN", "search_guest_placeholder": "Search for guests", "add_denied_guest": "<PERSON>d <PERSON>", "enter_first_name": "Enter First Name", "first_name_required": "First Name *", "enter_last_name": "Enter Last Name", "last_name_required": "Last Name *", "date_of_birth": "Date of Birth", "select_date": "Select a date", "enter_phone_number": "Enter Phone Number", "phone": "Phone", "enter_email": "<PERSON><PERSON>", "email": "Email", "enter_denial_reason": "Enter Denial Reason", "denial_reason_required": "Denial Reason *", "add_more": "Add More", "save": "Save", "create_new_visit": "Create a new visit", "unknown": "Unknown", "guest": "Guest", "not_available": "N/A", "screening_column": "Screening", "guest_name_column": "Name"}, "guest": {"all_guests": "All Guests", "today_guest": "Today Guest", "guest_name": "Guest Name", "email_id": "Email <PERSON>d", "company": "Company", "last_visited": "Last Visited", "next_visit": "Next Visit", "is_private": "Is Private?", "total_visits": "Total Visits", "actions": "Actions", "view": "View", "edit": "Edit", "history": "History", "view_guest": "View Guest", "edit_guest": "Edit Guest", "view_history": "View History", "my_guests": "My Guests"}, "my_access_areas": {"tabs": {"my_areas": "My Areas", "identities_with_my_areas": "Identities with My Areas"}, "area_name": "Area Name", "type": "Type", "start_date": "Start Date", "end_date": "End Date", "status": "Status", "assigned": "Assigned", "action": "Action"}, "my_audits": {"task_id": "Task ID", "description": "Description", "run_id": "Run ID", "task_owner": "Task Owner", "start_date": "Start Date", "end_date": "End Date", "recommend": "Recommend", "action": "Action", "retain": "<PERSON><PERSON>", "remove": "Remove", "open": "Open", "validation_task": "Validation Task", "validation_tasks": "Validation Tasks", "active": "Active", "all": "All"}, "my_events": {"event_title": "Event Title", "type": "Type", "category": "Category", "host": "Host", "escort": "Escort", "start_date_time": "Start Date & Time", "end_date_time": "End Date & Time", "status": "Status", "status_approved": "Approved", "actions": "Actions", "edit": "Edit", "block": "Block", "view_card": "View Card", "clone_card": "<PERSON><PERSON>", "my_events": "My Events", "today_events": "Today's Events"}, "my_profile": {"type": "Type", "employee": "Employee", "eid": "EID", "department": "Department", "manager": "Manager", "status": "Status", "unprinted_badges": "Unprinted Badges", "tabs": {"identity": "Identity", "corporate": "Corporate", "cards": "Cards", "access": "Access", "delegates": "Delegates", "vehicles": "Vehicles", "requests": "Requests"}}, "my_staff": {"title": "My Staff", "name": "Name", "uid": "UID", "type": "Type", "company": "Company", "organization": "Organization", "job_title": "Job Title", "manager": "Manager", "expiration_date": "Expiration Date", "status": "Status", "status_approved": "Approved", "open": "Open", "filter": {"direct_reports": "Direct Reports", "my_org": "My Org"}}, "my_request": {"request_id": "Request ID", "type": "Type", "requested_by": "Requested By", "created_on": "Created On", "justification": "Justification", "requested_for": "Requested For", "items": "Items", "status": "Status", "status_pending": "Pending", "my_requests": "My Requests"}, "my_task": {"task_id": "Task ID", "type": "Type", "request_for": "Request For", "items": "Item(s)", "request_id": "Request Id", "requested_by": "Requested By", "created_by": "Created By", "assignee": "Assignee", "justification": "Justification", "recommend": "Recommend", "task_hub": "Task Hub", "tasks": "Tasks", "all": "All", "active": "Active"}, "team_details": {"type": "Type", "employee": "Employee", "eid": "EID", "department": "Department", "manager": "Manager", "status": "Status", "unprinted_badges": "Unprinted Badges", "tabs": {"identity": "Identity", "corporate": "Corporate", "cards": "Cards", "access": "Access", "delegates": "Delegates", "vehicles": "Vehicles", "requests": "Requests"}}, "observation": {"name": "Name", "added_by": "Added By", "added_on": "Added On", "expiration_date": "Expiration Date", "status": "Status", "status_active": "Active", "status_inactive": "Inactive", "open": "Open", "roster_title": "Observation Roster", "filter_all": "All", "filter_active": "Active"}, "observation_details": {"title": "Observation Roster", "address": "Address", "status": "Status", "active": "Active", "phone_number": "Phone Number", "tabs": {"demographic_information": "Demographic Information", "reason_handling": "Reason & Handling", "document": "Document"}}, "patient_hub": {"title": "<PERSON><PERSON>", "search_patient_placeholder": "Search by Patient Name, MRN", "filter_all": "All", "filter_admitted": "Admitted", "tabs": {"demographic": "Demographic", "guest_list": "Guest List", "friends_family": "Friends & Family", "denied_guests": "Denied Guests", "hl7_messages": "HL7 Messages"}, "mrn": "MRN", "type": "Type", "status": "Status", "confidential": "Confidential", "facility": "Facility", "effective_date": "Effective Date", "changed_by": "Changed By", "event_type": "Event Type", "field_changed": "Field Changed", "from": "From", "to": "To", "view_history": "View History", "loading_patient_details": "Loading patient details...", "loading_history": "Loading history...", "no_history_found": "No history found for this patient.", "loading": "Loading..."}}