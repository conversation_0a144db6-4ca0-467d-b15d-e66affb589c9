import React, { useEffect, useState } from "react";
import Input from "./Input/Input";
import Button from "./Button";
import CustomDropdown from "./CustomDropdown";
import SearchableStringDropdown from "./SearchableDropdown";
import { FaPencil } from "react-icons/fa6";

const EditableSection = ({
  title,
  data,
  onChange,
  onSave,
  dropdownKeys = [],
  dropdownOptions = {},
  searchableKeys = [],
  editableKeys,
  editingButton = true,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [localData, setLocalData] = useState(data);

  useEffect(() => {
    setLocalData(data);
  }, [data]);

  const handleInputChange = (key, value) => {
    setLocalData((prev) => ({
      ...prev,
      [key]: { ...prev[key], value },
    }));
  };

  const handleSave = () => {
    Object.entries(localData).forEach(([key, { value }]) =>
      onChange(key, value)
    );
    if (onSave) onSave(localData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setLocalData(data);
    setIsEditing(false);
  };

  const isFieldEditable = (key) => {
    if (editableKeys && Array.isArray(editableKeys)) {
      return editableKeys.includes(key);
    }
    return true;
  };

  return (
    <div>
      <div className="bg-white px-4 pb-2 p-2 mb-2 shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] rounded-lg my-1">
        <div className="flex justify-between items-center">
          <h3 className="font-[500] text-[14px] text-[#4F2683] font-poppins">{title}</h3>
          {editingButton && !isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="rounded-full bg-[#4F2683] hover:bg-[#701ED8] p-1 mt-2"
            >
              <FaPencil className="text-white p-1" size={24} />
            </button>
          )}
        </div>
        <hr className="my-2" />
        <div>
          {isEditing ? (
            <>
              {Object.entries(localData).map(([key, { label, value }]) => (
                <div className="flex items-start mb-2" key={key}>
                  {/* Label Section */}
                  <div className="w-1/4 p-2">
                    <label
                      className="block text-[#7C7C7C] text-[12px] font-[400] font-poppins"
                      htmlFor={key}
                    >
                      {label}
                    </label>
                  </div>

                  {/* Input Section */}
                  <div className="w-3/4">
                    {isFieldEditable(key) ? (
                      dropdownKeys.includes(key) ? (
                        searchableKeys.includes(key) ? (
                          <SearchableStringDropdown
                            options={(dropdownOptions[key] || []).map((opt) => opt.value)}
                            value={value || ""}
                            onSelect={(option) => handleInputChange(key, option)}
                            placeholder="Select an option"
                            bgColor="bg-white"
                            textColor="text-black"
                            hoverBgColor="hover:bg-[#4F2683]"
                            borderColor="border-gray-300"
                            className="w-full h-10 text-[12px] font-[400] text-[#000] font-poppins"
                            rounded="rounded-md"
                          />
                        ) : (
                          <CustomDropdown
                            options={dropdownOptions[key] || []}
                            value={value || ""}
                            onSelect={(option) => handleInputChange(key, option)}
                            placeholder="Select an option"
                            searchable={false}
                            bgColor="bg-white"
                            textColor="text-black"
                            hoverBgColor="hover:bg-[#4F2683]"
                            borderColor="border-gray-300"
                            className="w-full h-10 text-[12px] font-[400] text-[#000] font-poppins"
                            rounded="rounded-md"
                          />
                        )
                      ) : (
                        <Input
                          type="text"
                          className="text-[12px] font-[400] text-[#000] font-poppins"
                          id={key}
                          label=""
                          value={value || ""}
                          onChange={(e) => handleInputChange(key, e.target.value)}
                        />
                      )
                    ) : (
                      <p className="text-[12px] font-[400] text-[#000] font-poppins">{value}</p>
                    )}
                  </div>
                </div>
              ))}
            </>
          ) : (
            <div>
              {Object.entries(localData).map(([key, { label, value }]) => (
                <div className="flex items-start mb-2" key={key}>
                  {/* Label Section */}
                  <div className="w-1/4">
                    <p className="text-[#7C7C7C] font-poppins text-[12px]">{label}</p>
                  </div>
                  {/* Value Section */}
                  <div className="w-3/4">
                    <p className="text-[#000] font-[400] font-poppins text-[12px]">{value}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      {isEditing && (
        <div className="flex gap-2 mb-4 justify-end">
          <Button type="cancel" label="Cancel" onClick={handleCancel} />
          <Button type="primary" label="Save Change" onClick={handleSave} />
        </div>
      )}
    </div>
  );
};

export default EditableSection;
