import { useState, useCallback, useEffect } from "react";
import { getBuildingsByFacility } from "../api/global";
import { toast } from "react-toastify";

// Module-level cache for building data keyed by facilityId
let buildingMasterDataCache = {};

export const useBuildingData = (facilityId) => {
  const [buildings, setBuildings] = useState(
    facilityId && buildingMasterDataCache[facilityId]
      ? buildingMasterDataCache[facilityId]
      : []
  );

  const fetchBuildings = useCallback(async () => {
    if (!facilityId) {
      console.log("useBuildingData: No facilityId provided");
      return;
    }

    console.log("useBuildingData: Fetching buildings for facilityId:", facilityId);
    try {
      const response = await getBuildingsByFacility(facilityId);
      console.log("useBuildingData: API response:", response);
      const buildingArray = response.data?.data || [];
      const fetchedBuildings = buildingArray.map((b) => ({
        label: b.name,
        value: b.building_id,
        code: b.building_code,
      }));
      console.log("useBuildingData: Mapped buildings:", fetchedBuildings);
      buildingMasterDataCache[facilityId] = fetchedBuildings;
      setBuildings(fetchedBuildings);
    } catch (error) {
      console.error("useBuildingData: Error fetching buildings:", error);
      toast.error("Error fetching buildings");
    }
  }, [facilityId]);

  useEffect(() => {
    fetchBuildings();
  }, [facilityId, fetchBuildings]);

  return buildings;
};
