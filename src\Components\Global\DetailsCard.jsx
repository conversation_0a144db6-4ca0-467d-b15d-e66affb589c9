import React from "react";
import { FaPen } from "react-icons/fa";
import { PiClockCountdown } from "react-icons/pi";


const DetailsCard = ({ name, OpenPhotoModal, profileImage, defaultImage, additionalFields = [], showHistoryButton, handleHistoryOpen }) => {

    return (
        <>
            <div className="h-[130px] bg-white rounded-[10px]  shadow-md flex items-center justify-between px-3 my-4">
                <div className="w-full flex items-center space-x-4">
                    <div className="w-[107.06px] h-[107.06px] relative group border border-gray-300 rounded-full flex-shrink-0 mx-auto">
                        <img
                            src={profileImage || defaultImage}
                            alt="Profile"
                            className="h-full w-full object-cover object-center rounded-full "
                        />
                        <button
                            onClick={OpenPhotoModal} // Open modal with title
                            className="absolute top-0 right-0 m-2 p-1 text-gray-500 hover:text-gray-700 bg-white rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                            <FaPen size={20} />
                        </button>
                    </div>
                    <div className="flex w-[calc(100%-127px)] flex-col justify-center">
                        <div className='flex justify-between'>
                            <h2 className="text-[#333333] text-[14px] font-[500] leading-normal">
                                {name}
                            </h2>
                            {showHistoryButton && (
                                <button
                                    className='flex gap-1 justify-center items-center'
                                    onClick={handleHistoryOpen}
                                ><PiClockCountdown className='font-[500] text-xl' /><span className='font-[400] text-[18px]'>View History</span>
                                </button>
                            )}
                        </div>
                        <hr className="my-2 border-t-2 border-gray-300" />
                        {/* 🆕 Dynamically Rendering Fields */}
                        <div className="flex gap-12 pr-4">
                            {additionalFields.map((field, index) => (
                                <div key={index} className=" flex flex-col items-start justify-center">
                                    <p className="text-[12px] text-[#7B7B7B] font-poppins">{field.label}</p>
                                    <p className="font-[400] text-[14px] text-[#000] font-poppins">{field.value}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}
export default DetailsCard;