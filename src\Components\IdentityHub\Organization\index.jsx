import React, { useState, useEffect, useCallback } from "react";
import EditableSection from "../../Global/EditabelForIdentity";
import { getIdentityOrganization, updateOrganization, updateFacility, fetchFacility } from "../../../api/identity";
import { useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import { useIdentityData } from "../../../hooks/useIdentityData";

const Organization = () => {
  const [searchParams] = useSearchParams();
  const identityId = searchParams.get("identity_id");
  const {identityStatusOptions } = useIdentityData();

  const [organizationData, setOrganizationData] = useState({
    Company: { label: "Company*", value: "" },
    CompanyCode: { label: "Company Code*", value: "" },
    Organization: { label: "ORG/Dept.", value: "" },
    EID: { label: "EID", value: "" },
    JobTitle: { label: "Job Title", value: "" },
    JobCode: { label: "Job Code", value: "" },
    Manager: { label: "Manager/Sponsor*", value: "" },
    Status: { label: "Status", value: "" },
  });

  const [facilityData, setFacilityData] = useState({
    Facility: { label: "Facility", value: "" },
    FacilityId: { label: "Facility ID", value: "" },
    Address: { label: "Address", value: "" },
    City: { label: "City*", value: "" },
    State: { label: "State/Province*", value: "" },
    Country: { label: "Country*", value: "" },
    PostalCode: { label: "Postal Code*", value: "" },
  });

  const [facilityOptions, setFacilityOptions] = useState([]);

  // Fetch facility options from API
  useEffect(() => {
    const fetchFacilities = async () => {
      try {
        const res = await fetchFacility();
        setFacilityOptions(
          Array.isArray(res?.data?.data)
            ? res.data.data.map((f) => ({
                label: f.name,
                value: f.name, // Use name as value
        id: f.facility_id // Store ID separately
              }))
            : []
        );
      } catch (error) {
        setFacilityOptions([]);
      }
    };
    fetchFacilities();
  }, []);

  const fetchIdentityData = useCallback(async () => {
    try {
      const { data } = await getIdentityOrganization(identityId);
      console.log(data);
      setOrganizationData({
        Company: { label: "Company*", value: data[0].company },
        CompanyCode: { label: "Company Code*", value: data[0].company_code },
        Organization: { label: "ORG/Dept.", value: data[0].organization },
        EID: { label: "EID", value: data[0].eid },
        JobTitle: { label: "Job Title", value: data[0].job_title },
        JobCode: { label: "Job Code", value: data[0].job_code },
        Manager: { label: "Manager/Sponsor*", value: data[0].manager },
        Status: { label: "Status", value: data[0].status },
      });

   setFacilityData({
  Facility: { 
    label: "Facility"|| "", 
    value: data[0].facility?.name || ""  // Store name as value
  },
  FacilityId: { 
    label: "Facility ID", 
    value: data[0].facility?.facility_id || "" 
  },
  Address: { label: "Address", value: data[0].facility?.address?.address_line_1 || "" },
  City: { label: "City*", value: data[0].facility?.address?.region || "" },
  State: { label: "State/Province*", value: data[0].facility?.address?.state?.name || "" },
  Country: { label: "Country*", value: data[0].facility?.address?.country?.name || "" },
  PostalCode: { label: "Postal Code*", value: data[0].facility?.address?.postal_code || "" },
});


    } catch (error) {
      toast.error("Failed to fetch organization data.");
      console.error("Error fetching organization data:", error);
    }
  }, [identityId]);

  useEffect(() => {
    if (identityId) {
      fetchIdentityData();
    } else {
      toast.error("Missing identity ID in URL.");
    }
  }, [identityId, fetchIdentityData]);

  const handleInputChange = (section, key, value) => {
  if (section === "facility" && key === "Facility") {
    // Find the selected facility object from facilityOptions
    const selectedFacility = facilityOptions.find((f) => f.value === value);
    setFacilityData((prev) => ({
      ...prev,
      [key]: {
      label: selectedFacility.label, // Update label with facility name
          value: selectedFacility.value, // Facility ID as value
      },
    }));
  }
};

  // Handle save for Organization section
  const handleOrganizationSave = async (data) => {
    try {
      const payload = {
        company: data.Company.value,
        company_code: data.CompanyCode.value,
        organization: data.Organization.value,
        eid: data.EID.value,
        job_title: data.JobTitle.value,
        job_code: data.JobCode.value,
        manager: data.Manager.value,
        status: data.Status.value,
        identity_id: identityId,
      };
      await updateOrganization(payload);
      toast.success("Organization updated successfully.");
      fetchIdentityData();
    } catch (error) {
      toast.error("Failed to update organization.");
    }
  };

  // Handle save for Facility section (only update facility_id and identity_id)
  const handleFacilitySave = async (data) => {
    try {
       const selectedFacility = facilityOptions.find(
      f => f.value === data.Facility.value
    );
      const payload = {
        facility_id: selectedFacility.id, // send id
        identity_id: identityId,
      };
      await updateFacility(payload);
      toast.success("Facility updated successfully.");
      fetchIdentityData();
    } catch (error) {
      toast.error("Failed to update facility.");
    }
  };

  return (
    <div className="bg-gray-100 min-h-screen">
      <EditableSection
        title="Organization Info"
        data={organizationData}
        onChange={(key, value) => handleInputChange("organization", key, value)}
        dropdownKeys={["Status"]}
        dropdownOptions={{
          Status: identityStatusOptions, // Assuming you have a StatusOptions array
       
        }}
        searchableKeys={["Email"]}
        onSave={handleOrganizationSave}
      />

      <EditableSection
        title="Facility Info"
        data={facilityData}
        onChange={(key, value) => handleInputChange("facility", key, value)}
        dropdownKeys={["Facility"]}
        dropdownOptions={{
          Facility: facilityOptions, // show facility names in dropdown
        }}
        searchableKeys={["Facility"]}
        editableKeys={["Facility"]}
        onSave={handleFacilitySave}
      />
    </div>
  );
};

export default Organization;
