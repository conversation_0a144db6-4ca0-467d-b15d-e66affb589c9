import React, { useState } from 'react';
import EditableSection from '../../Global/EditabelForIdentity';
import GenericTable from '../../GenericTable';
import TruncatedCell from '../../Tooltip/TruncatedCell';
import TruncatedRow from '../../Tooltip/TrucantedRow';

const Task = () => {
  // Restructure taskDetails and requestDetails to include label and value
  const [taskDetails, setTaskDetails] = useState({
    TaskType: { label: 'Task Type', value: 'Card Issuance' },
    TaskID: { label: 'Task ID', value: '2379836' },
    TaskOwner: { label: 'Task Owner', value: 'Badge Supervisor' },
    Status: { label: 'Status', value: 'Unprinted' },
    Justification: { label: 'Justification', value: 'It was damaged. Need a new card.' },
  });

  const [requestDetails, setRequestDetails] = useState({
    RequestDate: { label: 'Request Date', value: '19-Mar-2025' },
    RequestID: { label: 'Request ID', value: '17810919' },
    Shipping: { label: 'Shipping', value: 'Yes' },
    Status: { label: 'Status', value: 'Unprinted' },
    RequestedBy: { label: 'Requested By', value: 'GANGCAI HU, *********' },
    CardType: { label: 'Card Type', value: 'Permanent' },
    Comments: { label: 'Comments', value: 'Comments' },
  });

  const handleInputChange = (section, key, value) => {
    if (section === 'taskDetails') {
      setTaskDetails((prev) => ({ ...prev, [key]: { ...prev[key], value } }));
    } else if (section === 'requestDetails') {
      setRequestDetails((prev) => ({ ...prev, [key]: { ...prev[key], value } }));
    }
  };

  const taskColumns = [
    {
      name: <TruncatedCell text='Card Code' />,
      selector: (row) => row.cardCode,
      cell: (row) => <TruncatedRow text={row.cardCode} />,
    },
    {
      name: <TruncatedCell text='Card Type' />,
      selector: (row) => row.cardType,
      cell: (row) => <TruncatedRow text={row.cardType} />,
      sortable: true,
    },
    {
      name: <TruncatedCell text='Type' />,
      selector: (row) => row.type,
      cell: (row) => <TruncatedRow text={row.type} />,
    },
    {
      name: <TruncatedCell text='Template' />,
      selector: (row) => row.template,
      cell: (row) => <TruncatedRow text={row.template} />,
    },
    {
      name: <TruncatedCell text='Start Date' />,
      selector: (row) => row.startDate,
      cell: (row) => <TruncatedRow text={row.startDate} />,
    },
    {
      name: <TruncatedCell text='End Date' />,
      selector: (row) => row.endDate,
      cell: (row) => <TruncatedRow text={row.endDate} />,
    },
    {
      name: 'Status',
      selector: (row) => row.status,
      sortable: true,
      cell: (row) => (
        <span
          className={`w-24 py-1 items-center flex justify-center text-sm font-semibold rounded-full ${
            row.status.toLowerCase() === 'printed'
              ? 'bg-[#4F268314] bg-opacity-8 text-[#4F2683]'
              : 'bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]'
          }`}
        >
          {row.status}
        </span>
      ),
    },
  ];

  const taskData = [
    {
      cardCode: '1234',
      cardType: 'Permanent',
      type: 'Multitech General',
      template: 'Employee Badge - RWC - Remote Print',
      startDate: '20-Mar-2025',
      endDate: '20-Mar-2025',
      status: 'Unprinted',
    },
    {
      cardCode: '12345',
      cardType: 'Temporary',
      type: 'Multitech',
      template: 'Badge - RWC - Remote Print',
      startDate: '20-Mar-2025',
      endDate: '20-Mar-2025',
      status: 'Printed',
    },
  ];

  const identityColumns = [
    {
      name: 'Name',
      selector: (row) => row.name,
      cell: (row) => <TruncatedRow text={row.name} />,
    },
    {
      name: 'EID',
      selector: (row) => row.eid,
      cell: (row) => <TruncatedRow text={row.eid} />,
    },
    {
      name: 'Company',
      selector: (row) => row.company,
      cell: (row) => <TruncatedRow text={row.company} />,
    },
    {
      name: 'Type',
      selector: (row) => row.type,
      cell: (row) => <TruncatedRow text={row.type} />,
    },
    {
      name: 'Status',
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-24 py-1 items-center flex justify-center text-sm font-semibold rounded-full ${
            row.status.toLowerCase() === 'active'
              ? 'bg-[#4F268314] bg-opacity-8 text-[#4F2683]'
              : 'bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]'
          }`}
        >
          {row.status}
        </span>
      ),
    },
  ];

  const identityData = [
    {
      name: 'GANGCAI HU',
      eid: '*********',
      company: 'Oracle',
      type: 'Employee',
      status: 'Active',
    },
  ];

  return (
    <div className="p-4">
      <EditableSection
        title="Task Details"
        data={taskDetails}
        onChange={(key, value) => handleInputChange('taskDetails', key, value)}
      />

      <EditableSection
        title="Request Details"
        data={requestDetails}
        onChange={(key, value) => handleInputChange('requestDetails', key, value)}
      />

      <div className="mt-8 bg-white shadow-md rounded-lg">
        <GenericTable
          title="Identity"
          columns={identityColumns}
          data={identityData}
          showSearch={false}
          showAddButton={false}
        />
      </div>

      <div className="mt-8 bg-white shadow-md rounded-lg">
        <GenericTable
          title="Items"
          columns={taskColumns}
          data={taskData}
          showSearch={false}
          showAddButton={false}
        />
      </div>
    </div>
  );
};

export default Task;
