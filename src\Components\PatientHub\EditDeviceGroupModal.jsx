import React, { useEffect, useState } from "react";

const EditDeviceGroupModal = ({ isOpen, selectedRow, onClose, onSave }) => {
  const [editEntry, setEditEntry] = useState(selectedRow);
  const [show, setShow] = useState(false);

  useEffect(() => {
    setEditEntry(selectedRow);
  }, [selectedRow]);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => setShow(true), 10);
    } else {
      setShow(false);
    }
  }, [isOpen]);

  if (!isOpen || !editEntry) return null;

  const handleClose = () => {
    setShow(false);
    setTimeout(onClose, 700);
  };

  const handleSave = (e) => {
    e.preventDefault();
    onSave(editEntry);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-3xl bg-white rounded-lg shadow-lg h-full p-0 transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center mb-2 px-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Edit Device Group
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={handleClose}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form
          onSubmit={handleSave}
          className="bg-white p-6 rounded-lg"
        >
          <div className="flex items-center mb-4">
            <label htmlFor="deviceGroup" className="w-1/4 text-[16px] font-normal">
              Device Group
            </label>
            <div className="w-3/4">
              <input
                id="deviceGroup"
                name="deviceGroup"
                type="text"
                className="w-full border bg-transparent rounded p-2 focus:outline-none"
                value={editEntry.deviceGroup || ""}
                onChange={e => setEditEntry({ ...editEntry, deviceGroup: e.target.value })}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="assignApp" className="w-1/4 text-[16px] font-normal">
              Assign App
            </label>
            <div className="w-3/4">
              <input
                id="assignApp"
                name="assignApp"
                type="text"
                className="w-full border bg-transparent rounded p-2 focus:outline-none"
                value={editEntry.assignApp || ""}
                onChange={e => setEditEntry({ ...editEntry, assignApp: e.target.value })}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="status" className="w-1/4 text-[16px] font-normal">
              Status
            </label>
            <div className="w-3/4">
              <select
                id="status"
                name="status"
                className="w-full border bg-transparent rounded p-2 focus:outline-none"
                value={editEntry.status || "Active"}
                onChange={e => setEditEntry({ ...editEntry, status: e.target.value })}
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
              </select>
            </div>
          </div>
          <div className="flex gap-4 justify-end">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 bg-gray-400 text-white rounded"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#4F2683] text-white rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditDeviceGroupModal;
