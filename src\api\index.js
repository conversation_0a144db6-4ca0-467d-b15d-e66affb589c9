import axios from "axios";
// import { logout } from '../../src/utils/authSlice';
// import store from '../../src/utils/store';
const API_ENDPOINT = process.env.REACT_APP_API_URL;
// Helper functions to get tokens from localStorage
const getTokens = () => {
  const tokensStr = localStorage.getItem("tokens");
  return tokensStr ? JSON.parse(tokensStr) : null;
};

// const getAccessToken = () => {
//   const tokens = getTokens();
//   return tokens?.access?.token;
// };

const getRefreshToken = () => {
  
  const tokens = getTokens();
  return tokens?.refresh?.token;
};

// Helper to check token expiration.
// Assumes the token's "expires" field is a valid date string.
const isTokenExpired = (expiry) => {
  if (!expiry) return true;
  return new Date(expiry) < new Date();
};

// Variables to handle refresh flow
let isRefreshing = false;
let refreshSubscribers = [];

// When a new token is received, notify all waiting requests.
const subscribeTokenRefresh = (cb) => {
  refreshSubscribers.push(cb);
};

const onRefreshed = (newAccessToken) => {
  refreshSubscribers.forEach((cb) => cb(newAccessToken));
  refreshSubscribers = [];
};

export const api = axios.create({
  baseURL: API_ENDPOINT,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a request interceptor to dynamically set headers
api.interceptors.request.use(
  async (config) => {
    let tokens = getTokens();
    if (tokens && tokens.access) {
      // Check if access token is expired
      if (isTokenExpired(tokens.access.expires)) {
        if (!isRefreshing) {
          isRefreshing = true;
          try {
            // Use a separate axios instance (or axios directly) to avoid interceptor loops
            const refreshToken = getRefreshToken();
            const response = await axios.post(
              `${API_ENDPOINT}/auth/refresh-tokens`,
              { refreshToken }
            );
            // Assume the refresh API returns the new tokens
            const newTokens = response.data;
            // Store the new tokens in localStorage
            localStorage.setItem("tokens", JSON.stringify(newTokens.data));
            tokens = newTokens.data;
            isRefreshing = false;
            // Notify all waiting subscribers with the new access token
            onRefreshed(tokens.access.token);
          } catch (error) {
            isRefreshing = false;
            // Refresh failed – clear stored tokens (i.e. log the user out)
            localStorage.removeItem("tokens");
            localStorage.removeItem("identity");
            return Promise.reject({ ...error, isAuthError: true });
          }
        }

        // Queue the current request until the token refresh is complete
        const retryOriginalRequest = new Promise((resolve) => {
          subscribeTokenRefresh((newAccessToken) => {
            config.headers["Authorization"] = `Bearer ${newAccessToken}`;
            resolve(config);
          });
        });
        return retryOriginalRequest;
      } else {
        // Access token is valid; attach it to the request
        config.headers["Authorization"] = `Bearer ${tokens.access.token}`;
      }
    }

    // Dynamically set Content-Type if needed
    if (config.data instanceof FormData) {
      config.headers["Content-Type"] = "multipart/form-data";
    } else if (config.data) {
      config.headers["Content-Type"] = "application/json";
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle errors globally
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      // store.dispatch(logout());
      localStorage.removeItem("tokens");
      localStorage.removeItem("identity");
      window.location.href = "/login";
      // return Promise.reject({ ...error, isAuthError: true });
    }
    return Promise.reject(error);
  }
);

export default api;
