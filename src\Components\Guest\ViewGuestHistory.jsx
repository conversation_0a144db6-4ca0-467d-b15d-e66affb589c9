import React, { useState } from "react";
import GenericTable from "../../Components/GenericTable"; // Import GenericTable
import DetailsCard from "../../Components/Global/DetailsCard"; // Import DetailsCard
import userImg from "../../Images/guest-image2.png"; // Import default user image

const ViewGuestHistory = ({ guest, onClose }) => {
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const columns = [
    { name: "Guest ID", selector: (row) => row.guestId },
    { name: "Category", selector: (row) => row.category },
    { name: "Host", selector: (row) => row.host },
    { name: "Check In Time", selector: (row) => row.checkInTime },
    { name: "Check Out Time", selector: (row) => row.checkOutTime },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center  rounded-full ${row.status.toLowerCase() === "check-in"
            ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
            : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
            }`}
        >
          {row.status}
        </span>
      ),
    },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]"> View Check In History</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

            <div className="p-6">
              <DetailsCard
                OpenPhotoModal={() => { }}
                profileImage={guest.profileImage || userImg}
                defaultImage={userImg}
                name={guest.name}
                additionalFields={[
                  { label: "Company", value: guest.eid || "N/A" },
                  { label: "Gmail", value: guest.company || "N/A" },
                  { label: "Is Private?", value: guest.email || "N/A" },
                ]}
              />

              <GenericTable
                title="Guest History"
                columns={columns}
                // data={historyData}
                showAddButton={false} // No add button needed
              />
              <div className="flex justify-center gap-4 mt-6">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-400 text-white rounded"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};


export default ViewGuestHistory;
