import React, { useState, useEffect, useRef } from "react";
import { FaChevronDown } from "react-icons/fa";

const CustomDropdown = ({
  options,
  defaultValue,
  value,
  onSelect,
  bgColor = "",
  textColor = "",
  hoverBgColor = "hover:bg-[#4F2683]",
  borderColor = "border-gray-300",
  className = "",
  rounded = "rounded",
  placeholder = "",
  dropDownclassName,
  error,
  disabled = false, // Added disabled prop
  loading = false,
}) => {
  const dropdownRef = useRef(null);

  // Helper functions to get label and value from an option.
  const getOptionLabel = (option) => {
    if (typeof option === "object" && option !== null && option.label !== undefined) {
      return option.label;
    }
    return option;
  };

  // const getOptionValue = (option) => {
  //   if (typeof option === "object" && option !== null) {
  //     return option.key !== undefined ? option.key : option.value;
  //   }
  //   return option;
  // };
  const getOptionValue = (option) => {
    if (typeof option === "object" && option !== null) {
      return option.key ?? option.value;
    }
    return option;
  };


  // Initialize the selected option.
  const [selectedOption, setSelectedOption] = useState(() => {
    if (value !== undefined) {
      const match = (options || []).find(opt => getOptionValue(opt) === value);
      return match ? match : value;
    }
    if (defaultValue !== undefined) {
      const match = (options || []).find(opt => getOptionValue(opt) === defaultValue);
      return match ? match : defaultValue;
    }
    return null;
  });

  const [isOpen, setIsOpen] = useState(false);

  // When the controlled value changes, update internal state.
  useEffect(() => {
    if (value !== undefined) {
      const match = (options || []).find(opt => getOptionValue(opt) === value);
      setSelectedOption(match ? match : value);
    }
  }, [value, options, defaultValue]);

  const handleSelect = (option) => {
    if (disabled) return; // Prevent selection when disabled

    // const valueToPass = option?.value ?? (typeof option === "string" ? option : "");
    const valueToPass = option?.key ?? option?.value ?? (typeof option === "string" ? option : "");


    if (onSelect) {
      onSelect(valueToPass);
    }
    setSelectedOption(option);
    setIsOpen(false);
  };

  // Close the dropdown if clicked outside.
  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  return (
    <div ref={dropdownRef} className="relative w-full">
      {/* Selected Option */}
      <div
        className={`flex items-center justify-between px-2 py-1 cursor-pointer border ${rounded} ${bgColor} ${textColor} ${borderColor} ${className} ${dropDownclassName} ${isOpen && !disabled ? "outline-none ring-1 ring-[#4F2683]" : ""
          } ${disabled ? "bg-gray-100 cursor-not-allowed opacity-70" : ""
          }`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        {selectedOption ? (
          getOptionLabel(selectedOption)
        ) : (
          <span className={`${disabled ? "text-gray-500" : "text-[#8A929F]"}`}>
            {placeholder}
          </span>
        )}
        <FaChevronDown className={disabled ? "text-gray-500" : ""} />
      </div>

      {/* Dropdown Options */}
      {/* {isOpen && !disabled && (
        <ul className={`absolute w-full border ${borderColor} shadow-lg text-black mt-1 rounded-md z-10 bg-white max-h-60 overflow-y-auto`}>
          {(options || []).map((option, index) => (
            <li
              key={index}
              className={`px-4 py-2 cursor-pointer ${hoverBgColor} hover:text-white`}
              onClick={() => handleSelect(option)}
            >
              {getOptionLabel(option)}
            </li>
          ))}
        </ul>
      )} */}
      {isOpen && !disabled && (
        <ul className={`absolute w-full border ${borderColor} shadow-lg text-black mt-1 rounded-md z-10 bg-white max-h-60 overflow-y-auto`}>
          {loading ? (
            <li className="px-4 py-2 text-gray-500 cursor-default">Loading...</li>
          ) : (options || []).length > 0 ? (
            (options || []).map((option, index) => (
              <li
                key={index}
                className={`px-4 py-2 cursor-pointer ${hoverBgColor} hover:text-white`}
                onClick={() => handleSelect(option)}
              >
                {getOptionLabel(option)}
              </li>
            ))
          ) : (
            <li className="px-4 py-2 text-gray-500 cursor-default">No options available</li>
          )}
        </ul>
      )}

      {error && <p className="text-red-500">{error?.message}</p>}
    </div>
  );
};

export default CustomDropdown;