import React from 'react';
import { useSelector } from 'react-redux';
import { Navigate, Outlet } from 'react-router-dom';

const ProtectedRoute = ({ redirectPath = '/login' }) => {
  const { tokens } = useSelector((state) => state.auth);

  // Check for a valid access token (you can adjust the logic as needed)
  if (!tokens || !tokens.access || !tokens.access.token) {
    return <Navigate to={redirectPath} replace />;
  }
  
  return <Outlet />;
};

export default ProtectedRoute;
