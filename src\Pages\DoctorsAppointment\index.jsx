import React, { useState, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import SearchBar from "../../Components/Global/SearchBar.jsx";
import TabsComponent from "../../Components/Global/TabsComponent.jsx";
import homeicon from "../../Images/home-icon.svg";
import GenericTable from "../../Components/GenericTable";
import WalkInVisitForm from "../../Components/DoctorsAppointment/WalkInVisitForm.jsx";
import PatientCard from "../../Components/Global/PatientCard.jsx";
import Button from "../../Components/Global/Button.jsx";
// import { OutPatientListDetails2 } from "../../api/static.js";
import CameraIcon from "../../Images/camera.svg";
import ChatIcon from "../../Images/chat.svg";
import demo from "../../Images/demoimg.svg";
import PrintIcon from "../../Images/print.svg";
import InIcon from "../../Images/in.svg";
import OutIcon from "../../Images/out.svg";
import PrintModal from "../../Components/Global/PrintModal";
import EditPhotoModal from "../../Components/Global/ImageAndCamera/EditPhotoModal.jsx";
import TruncatedCell from "../../Components/Tooltip/TruncatedCell.jsx";
import {
  getAppointments,
  getAppointmentById,
  checkGuest,
  getOutpatientAppointmentsByPatientId,
  getFutureAppointmentsByPatientId,
  checkAppointment
} from "../../api/Appointments";
// import card from "../../Images/Cardidenty.svg";
import formatDateTime from "../../utils/formatDate.js";
import formatTime from "../../utils/formatDateTime.js";
import PlusIcon from "../../Images/add-guest.svg";
import { IoMdAlert } from "react-icons/io";

// -------------------- Utility Functions --------------------
const filterAppointmentsBySearch = (appointments, searchQuery, fields) => {
  if (!Array.isArray(appointments)) return [];
  if (!searchQuery) return appointments;
  const lowerCaseQuery = searchQuery.toLowerCase();
  return appointments.filter((appointment) =>
    fields.some((field) => {
      const value = appointment[field];
      return value && typeof value === "string" && value.toLowerCase().includes(lowerCaseQuery);
    })
  );
};

// const isToday = (dateString) => {
//   const today = new Date();
//   const [month, day, year] = dateString.split("/");
//   const appointmentDate = new Date(`${month}/${day}/${year}`);
//   return (
//     appointmentDate.getDate() === today.getDate() &&
//     appointmentDate.getMonth() === today.getMonth() &&
//     appointmentDate.getFullYear() === today.getFullYear()
//   );
// };

const mapOutpatientAppointment = (a) => ({
  name: `${a.first_name} ${a.last_name}`,
  guest_pin: a.guest_pin,
  mrn: a.guest_pin,
  appointmentTime: formatTime(a.appointment_date),
  arrivalTime: formatTime(a.appointment_arrival_time),
  departureTime: formatTime(a.appointment_departure_time),
  location: a.location,
  screening: a.screening || false,
  appointment_id: a.appointment_id,
  appointment_guest_id: a.appointment_guest_id,
  guest_type: a.guest_type,
});

// -------------------- Main Component --------------------
const DoctorsAppointment = () => {
  const { t } = useTranslation();

  // -------------------- State Variables --------------------
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [todaysSortBy, setTodaysSortBy] = useState("");
  const [todaysSortOrder, setTodaysSortOrder] = useState("");
  const [futureSortBy, setFutureSortBy] = useState("");
  const [futureSortOrder, setFutureSortOrder] = useState("");
  const [patientId, setPatientId] = useState(null);
  const [appointmentId, setAppointmentId] = useState(null);
  const [todaysAppointments, setTodaysAppointments] = useState([]);
  const [futureAppointments, setFutureAppointments] = useState([]);
  const [searchQueryToday, setSearchQueryToday] = useState("");
  const [searchQueryFuture, setSearchQueryFuture] = useState("");
  const [showWalkInVisitForm, setShowWalkInVisitForm] = useState(false);
  const [isEditPhotoModalOpen, setIsEditPhotoModalOpen] = useState(false);
  const [selectedGuestId, setSelectedGuestId] = useState(null);
  const [printModalVisible, setPrintModalVisible] = useState(false);
  const [selectedGuest, setSelectedGuest] = useState(null);

  // -------------------- Effects: Fetch Appointments --------------------
  useEffect(() => {
    const fetchAppointments = async () => {
      if (!patientId) return;
      try {
        const outpatientResponse = await getOutpatientAppointmentsByPatientId(patientId, todaysSortBy, todaysSortOrder).catch(err => {
          console.error("Error fetching outpatient appointments:", err);
          return null;
        });
        setTodaysAppointments((outpatientResponse?.data?.data || []).map(mapOutpatientAppointment));
      } catch (error) {
        console.error("Error fetching appointments:", error);
      }
    };
    fetchAppointments();
  }, [patientId, todaysSortBy, todaysSortOrder]);

  useEffect(() => {
    const fetchAppointments = async () => {
      if (!patientId) return;
      try {
        const futureResponse = await getFutureAppointmentsByPatientId(patientId, futureSortBy, futureSortOrder);
        const formattedFutureAppointments = futureResponse?.data?.data?.map((appointment) => ({
          name: `${appointment.first_name} ${appointment.last_name}`,
          provider_name: appointment.provider_name,
          mrn: appointment.guest_pin,
          appointmentTime: formatTime(appointment.appointment_date),
          arrivalTime: formatTime(appointment.appointment_arrival_time),
          departureTime: formatTime(appointment.appointment_departure_time),
          location: appointment.location,
          screening: appointment.screening || false,
          guest_type: appointment.appointment_guest_id ? 'g' : 'p',
          appointment_id: appointment.appointment_id,
          appointment_guest_id: appointment.appointment_guest_id,
        })) || [];
        setFutureAppointments(formattedFutureAppointments);
      } catch (error) {
        console.error("Error fetching appointments:", error);
      }
    };
    fetchAppointments();
  }, [patientId, futureSortBy, futureSortOrder]);

  // -------------------- Handlers --------------------
  const handleInputChange = async (input) => {
    const value = input && input.target ? input.target.value : input;
    setSearchTerm(value);
    if (value) {
      try {
        const response = await getAppointments({ search: value, type: 1 });
        setSearchResults(response?.data || []);
        setIsDropdownVisible(true);
      } catch (error) {
        setSearchResults([]);
        setIsDropdownVisible(false);
      }
    } else {
      setSearchResults([]);
      setIsDropdownVisible(false);
    }
  };

  const handleTodaysSortClick = async (column, sortDirection) => {
    setTodaysSortBy(column?.id);
    setTodaysSortOrder(sortDirection.toUpperCase());
  };

  const handleFutureSortClick = (column, sortDirection) => {
    setFutureSortBy(column?.id);
    setFutureSortOrder(sortDirection.toUpperCase());
  };

  const handleCheckIn = async (row) => {
    try {
      const idToUse = row.guest_type === 'g'
        ? row.appointment_guest_id
        : row.appointment_id;
      if (row.guest_type === 'g') {
        await checkGuest(idToUse, "checkIn", row.appointment_id);
      } else {
        await checkAppointment(idToUse, "checkIn");
      }
      setTodaysAppointments(prev =>
        prev.map(a =>
          a.appointment_id === row.appointment_id &&
            a.appointment_guest_id === row.appointment_guest_id
            ? { ...a, arrivalTime: new Date().toISOString() }
            : a
        )
      );
    } catch (error) {
      // handle error
    }
  };

  const handleCheckOut = async (row) => {
    try {
      const endpointId = row.guest_type === 'g'
        ? row.appointment_guest_id
        : row.appointment_id;
      setTodaysAppointments(prev => prev.map(a =>
        a.appointment_id === row.appointment_id &&
          a.appointment_guest_id === row.appointment_guest_id
          ? { ...a, departureTime: new Date().toISOString() }
          : a
      ));
      if (row.guest_type === 'p') {
        await checkAppointment(endpointId, "checkOut");
      } else {
        await checkGuest(endpointId, "checkOut", row.appointment_id);
      }
      const resp = await getOutpatientAppointmentsByPatientId(patientId);
      setTodaysAppointments((resp.data.data || []).map(mapOutpatientAppointment));
    } catch (error) {
      // handle error
    }
  };

  const handlePatientClick = async (patient) => {
    if (!patient || !patient.patient_id || !patient.appointment_id) return;
    setSearchTerm("");
    setSearchResults([]);
    setIsDropdownVisible(false);
    setShowWalkInVisitForm(false);
    setPatientId(patient.patient_id);
    setAppointmentId(patient.appointment_id);
    try {
      const response = await getAppointmentById(patient.appointment_id);
      setSelectedPatient(response.data);
    } catch (error) {
      // handle error
    }
  };

  const openEditPhotoModal = (title, id) => {
    setSelectedGuestId(id);
    setIsEditPhotoModalOpen(true);
  };

  const handleImageCaptured = (imageData) => {
    if (selectedPatient && selectedPatient.appointment) {
      const updatedAppointments = selectedPatient.appointment.map((appointment) =>
        appointment.id === selectedGuestId
          ? { ...appointment, image: imageData }
          : appointment
      );
      setSelectedPatient({ ...selectedPatient, appointment: updatedAppointments });
    }
    setIsEditPhotoModalOpen(false);
  };

  const handlePrintClick = (row) => {
    setSelectedGuest(row);
    setPrintModalVisible(true);
  };

  const handleClosePrintModal = () => {
    setPrintModalVisible(false);
    setSelectedGuest(null);
  };

  const handleHome = () => {
    if (selectedPatient || selectedGuest) {
      setSelectedPatient(null);
      setSelectedGuest(null);
    }
  };

  const handleAddGuest = async () => {
    if (!patientId) return;
    try {
      const resp = await getOutpatientAppointmentsByPatientId(patientId);
      const updated = (resp.data.data || []).map(mapOutpatientAppointment);
      setTodaysAppointments(updated);
    } catch (error) {
      // handle error
    }
  };

  // -------------------- Table Columns --------------------
  const todaysColumns = [
    {
      id: "first_name",
      name: t('doctors_appointment.name'),
      cell: (row) => (
        <div className="flex items-center">
          <img
            src={row?.image || demo}
            alt={row?.name}
            className="w-8 h-8 rounded-full mr-2"
          />
          <span>{row?.name}</span>
        </div>
      ),
      sortable: true,
    },
    {
      id: "guest_pin",
      name: t('doctors_appointment.guest_pin'),
      cell: (row) => <div>{row?.mrn}</div>,
      width: "11%",
      sortable: true,
    },
    {
      name: t('doctors_appointment.screening'),
      cell: (row) => (
        row.guest_type === 'g' ? (
          <div className="flex items-center">
            <Button
              type="toggle"
              initialState={!row.screening}
              onClick={(state) => {}}
              disabled={true}
            />
            {row.screening === 1 && (
              <IoMdAlert
                className="ml-2 text-red-500 cursor-pointer"
                title={t('doctors_appointment.screening_alert')}
                size={20}
                onClick={() => {}}
              />
            )}
          </div>
        ) : null
      ),
      selector: (row) => row.screening,
      width: "10%",
      sortable: false,
    },
    {
      name: t('doctors_appointment.appointment_time'),
      cell: (row) => row.appointmentTime,
      sortable: true,
      width: "18%",
    },
    {
      id: "appointment_arrival_time",
      name: <TruncatedCell text={t('doctors_appointment.arrival_time')} />,
      cell: (row) => row.arrivalTime,
      width: "12%",
      sortable: true,
    },
    {
      id: "location",
      name: t('doctors_appointment.location'),
      cell: (row) => <span>{row.location}</span>,
      width: "12%",
      sortable: true,
    },
    {
      name: t('doctors_appointment.action'),
      cell: (row) => (
        <div className="flex justify-center items-center space-x-2">
          {row.guest_pin === null ? (
            <img
              src={PlusIcon}
              alt={t('doctors_appointment.add_guest')}
              title={t('doctors_appointment.add_guest')}
              className="p-2 w-9 h-9 rounded-lg cursor-pointer bg-[#F0EDF5]"
              onClick={() => setShowWalkInVisitForm(true)}
            />
          ) : (
            <img
              src={PlusIcon}
              alt="plus"
              className="p-2 w-9 h-9 rounded-lg bg-[#F0EDF5] opacity-0"
            />
          )}
          <img
            src={CameraIcon}
            alt={t('doctors_appointment.camera')}
            title={t('doctors_appointment.capture_picture')}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => openEditPhotoModal(t('doctors_appointment.update_profile_image'), row.id)}
          />
          <img
            src={ChatIcon}
            alt={t('doctors_appointment.chat')}
            title={t('doctors_appointment.nda')}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => {}}
          />
          <img
            src={PrintIcon}
            alt={t('doctors_appointment.print')}
            title={t('doctors_appointment.print_guest_label')}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => handlePrintClick(row)}
          />
          <img
            src={InIcon}
            alt={t('doctors_appointment.check_in')}
            title={t('doctors_appointment.guest_check_in')}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => handleCheckIn(row)}
          />
          <img
            src={OutIcon}
            alt={t('doctors_appointment.check_out')}
            title={t('doctors_appointment.guest_check_out')}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => handleCheckOut(row)}
          />
        </div>
      ),
      center: true,
      sortable: false,
    },
  ];

  const futureColumns = [
    {
      id: "first_name",
      name: t('doctors_appointment.patient_name'),
      cell: (row) => (
        <div className="flex items-center">
          <img
            src={row.image || demo}
            alt={row.name}
            className="w-8 h-8 rounded-full mr-2"
          />
          <span>{row.name}</span>
        </div>
      ),
      sortable: true,
    },
    {
      id: "guest_pin",
      name: t('doctors_appointment.guest_pin'),
      cell: (row) => <span>{row.mrn}</span>,
      width: "11%",
      sortable: true,
    },
    {
      id: "provider_name",
      name: <TruncatedCell text={t('doctors_appointment.doctors_name')} />,
      cell: (row) => <span>{row.provider_name}</span>,
      width: "15%",
      sortable: true,
    },
    {
      id: "appointment_arrival_time",
      name: t('doctors_appointment.appointment_time'),
      cell: (row) => row.appointmentTime,
      sortable: true,
      width: "24%",
    },
    {
      id: "location",
      name: t('doctors_appointment.location'),
      cell: (row) => (
        <span>{row.room ? `${row.room}, Bed-${row.bed}` : ""}</span>
      ),
      width: "12%",
      sortable: true,
    },
    {
      name: t('doctors_appointment.action'),
      cell: (row) => (
        <div className="flex justify-center items-center space-x-2">
          <img
            src={CameraIcon}
            alt={t('doctors_appointment.camera')}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => openEditPhotoModal(t('doctors_appointment.update_profile_image'), row.id)}
          />
          <img
            src={ChatIcon}
            alt={t('doctors_appointment.chat')}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => {}}
          />
          <img
            src={PrintIcon}
            alt={t('doctors_appointment.print')}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => handlePrintClick(row)}
          />
          <img
            src={InIcon}
            alt={t('doctors_appointment.in')}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => {}}
          />
          <img
            src={OutIcon}
            alt={t('doctors_appointment.out')}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => {}}
          />
        </div>
      ),
      center: true,
    },
  ];

  // -------------------- Filtered Data --------------------
  const filteredTodaysAppointments = filterAppointmentsBySearch(
    Array.isArray(todaysAppointments) ? todaysAppointments : [],
    searchQueryToday,
    ["name", "mrn", "appointmentTime", "room"]
  );
  const filteredFutureAppointments = filterAppointmentsBySearch(
    Array.isArray(futureAppointments) ? futureAppointments : [],
    searchQueryFuture,
    ["patientName", "doctor", "appointmentTime", "room"]
  );

  // -------------------- Render --------------------
  return (
    <div className="pl-24 pr-8 mb-4">
      <TabsComponent />
      <div className="flex justify-center mt-3 items-center gap-4">
        <div>
          <Button type="imgbtn" className="px-[10px] py-2" icon={homeicon} onClick={handleHome} />
        </div>
        <div className="relative">
          <SearchBar
            placeholder={t('doctors_appointment.search_patient_placeholder')}
            onInputChange={handleInputChange}
            value={searchTerm}
            borderColor="#4F2683"
          />
          {isDropdownVisible && (
            <div
              className="w-full mt-2 border absolute p-2 bg-white rounded-md shadow-lg overflow-y-auto"
              style={{ maxHeight: "200px" }}
            >
              {searchResults.length > 0 ? (
                searchResults.map((patient) => (
                  <div
                    key={patient.appointment_id}
                    className="flex items-center gap-3 p-2 cursor-pointer hover:bg-gray-100"
                    onClick={() => handlePatientClick(patient)}
                  >
                    <img
                      src={patient.image || demo}
                      alt={patient.patient_name}
                      className="w-10 h-10 rounded-md"
                    />
                    <div>
                      <h2 className="font-semibold">{patient.patient_name}</h2>
                      <div className="flex flex-row gap-1">
                        <p className="text-[12px] text-gray-600">
                          {formatDateTime(patient.birth_date)}, MRN : {patient.mrn}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-1 text-center text-gray-700">
                  <Button label={t('doctors_appointment.no_results_found')} />
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      {isEditPhotoModalOpen && (
        <EditPhotoModal
          onClose={() => setIsEditPhotoModalOpen(false)}
          onSave={handleImageCaptured}
        />
      )}
      {printModalVisible && selectedGuest && (
        <PrintModal guest={selectedGuest} onClose={handleClosePrintModal} />
      )}

      {selectedPatient && (
        <div>
          <PatientCard appointmentId={selectedPatient.appointment_id} hideFields={true} />
          {showWalkInVisitForm && (
            <WalkInVisitForm
              fieldsToRender={[
                "firstName",
                "lastName",
                "dob",
                "guestMail",
                "phoneNumber",
                "relationship",
              ]}
              appointmentId={appointmentId}
              onAddGuest={handleAddGuest}
              onClose={() => setShowWalkInVisitForm(false)}
            />
          )}

          {/* Today's Appointments Table */}
          <div className="mb-6">
            <GenericTable
              title={t('doctors_appointment.todays_appointments')}
              searchTerm={searchQueryToday}
              onSearchChange={(e) => setSearchQueryToday(e.target.value)}
              onSort={handleTodaysSortClick}
              filter={""}
              onFilterChange={() => { }}
              filterOptions={[]}
              columns={todaysColumns}
              data={filteredTodaysAppointments}
              rowClassName={row =>
                row.guest_pin === null
                  ? "bg-red-200 hover:bg-red-300"
                  : ""
              }
              fixedHeader
              fixedHeaderScrollHeight="300px"
              highlightOnHover
              striped={false}
              alwaysShowSearchInput={true}
              showAddButton={false}
            />
          </div>

          {/* All Future Appointments Table */}
          <div className="mb-6">
            <GenericTable
              title={t('doctors_appointment.all_future_appointments')}
              searchTerm={searchQueryFuture}
              onSearchChange={(e) => setSearchQueryFuture(e.target.value)}
              onSort={handleFutureSortClick}
              onAdd={() => { }}
              filter={""}
              onFilterChange={() => { }}
              filterOptions={[]}
              columns={futureColumns}
              data={filteredFutureAppointments}
              fixedHeader
              fixedHeaderScrollHeight="300px"
              highlightOnHover
              striped={false}
              showAddButton={false}
              alwaysShowSearchInput={true}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default DoctorsAppointment;