import React, { useState } from "react";
import GenericTable from "../../GenericTable";
import RequestsView from "./RequestsView";
import { Tooltip } from "react-tooltip";
import TruncatedCell from "../../Tooltip/TruncatedCell"
import TruncatedRow from "../../Tooltip/TrucantedRow";


const Requests = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [requests, setRequests] = useState([
    {
      id: 1,
      requestId: "51624",
      type: "Access",
      justification: "Area Testing",
      requestedBy: "Admin",
      startActive: "2024-01-31",
      endRemoval: "2024-02-05",
      createdOn: "2024-01-31",
      status: "Active",
    },
    {
      id: 2,
      requestId: "51625",
      type: "Maintenance",
      justification: "Routine check",
      requestedBy: "John Doe",
      startActive: "2024-02-01",
      endRemoval: "2024-02-10",
      createdOn: "2024-02-01",
      status: "Inactive",
    },
  ]);

  const [showView, setShowView] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);

  const handleView = (req) => {
    setSelectedRequest(req);
    setShowView(true);
  };
  const columns = [
    {
      name:<TruncatedCell text="Request ID"/>,
      selector: (row) => row.requestId,
      cell: (row) => (
        <span
          className="underline underline-offset-1 cursor-pointer"
          onClick={() => handleView(row)}
        >
          <TruncatedRow text={row.requestId}/>
        </span>
      ),
    },
    {
      name: <TruncatedCell text="Type"/>,
      selector: (row) => row.type,
      cell: (row) => <TruncatedRow text={row.type} />,
    },
    {
      name:<TruncatedCell text="Justification"/>,
      selector: (row) => row.justification,
      cell: (row) => <TruncatedRow text={row.justification} />,
    },
    {
      name: <TruncatedCell text="Requested By"/>,
      selector: (row) => row.requestedBy,
      cell: (row) => <TruncatedRow text={row.requestedBy} />,
    },
    {
      name: <TruncatedCell text="Start/Active"/>,
      selector: (row) => row.startActive,
      cell: (row) => <TruncatedRow text={row.startActive} />,
    },
    {
      name:<TruncatedCell text="End/Removal"/>,
      selector: (row) => row.endRemoval,
      cell: (row) => <TruncatedRow text={row.endRemoval} />,
    },
    {
      name: <TruncatedCell text="Created On"/>,
      selector: (row) => row.createdOn,
      cell: (row) => <TruncatedRow text={row.createdOn} />,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center text-sm rounded-full ${
            row.status === "Active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
  ];

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Requests"
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        columns={columns}
        data={requests}
        showSearch={true}
        showAddButton={false} // No "Add" button
      />

      {/* Modal for viewing a single request (read-only) */}
      {showView && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white p-1 shadow-lg rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <RequestsView
                requestData={selectedRequest}
                onClose={() => setShowView(false)}
              />
            </div>
          </div>
        </div>
      )}

      {/* Ensure tooltips are always rendered */}
      <Tooltip id="requests-header-tooltip" place="top" effect="solid" />
    </div>
  );
};

export default Requests;
